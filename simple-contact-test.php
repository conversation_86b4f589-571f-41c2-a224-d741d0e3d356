<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Simple Contact Form Test</h1>";

// Include required files
require_once 'includes/db.php';
require_once 'includes/functions.php';

$message = '';
$error = '';

if ($_POST && isset($_POST['submit_test'])) {
    echo "<h2>Processing Form Submission...</h2>";
    
    // Get form data
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $subject = $_POST['subject'] ?? '';
    $message_text = $_POST['message'] ?? '';
    $business_type = $_POST['business_type'] ?? '';
    
    echo "<p>Name: " . htmlspecialchars($name) . "</p>";
    echo "<p>Email: " . htmlspecialchars($email) . "</p>";
    echo "<p>Subject: " . htmlspecialchars($subject) . "</p>";
    echo "<p>Business Type: " . htmlspecialchars($business_type) . "</p>";
    
    // Basic validation
    if (empty($name) || empty($email) || empty($subject) || empty($message_text)) {
        $error = 'Please fill in all required fields.';
        echo "<p style='color: red;'>❌ Validation failed: $error</p>";
    } else {
        echo "<p style='color: green;'>✅ Validation passed</p>";
        
        // Check database connection
        if (!$conn) {
            $error = 'Database connection failed';
            echo "<p style='color: red;'>❌ $error</p>";
        } else {
            echo "<p style='color: green;'>✅ Database connected</p>";
            
            // Prepare data
            $data = [
                'name' => $name,
                'email' => $email,
                'phone' => '',
                'company' => '',
                'subject' => $subject,
                'message' => $message_text,
                'service_interest' => '',
                'business_type' => $business_type,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'status' => 'new'
            ];
            
            echo "<p>Attempting to insert data...</p>";
            
            try {
                $result = insertRecord($conn, 'contact_messages', $data);
                if ($result) {
                    $message = "✅ Message saved successfully! ID: $result";
                    echo "<p style='color: green;'>$message</p>";
                    
                    // Verify the insert
                    $saved = fetchOne($conn, "SELECT * FROM contact_messages WHERE id = ?", [$result]);
                    if ($saved) {
                        echo "<p style='color: green;'>✅ Message verified in database</p>";
                        echo "<p>Saved: " . htmlspecialchars($saved['name']) . " - " . htmlspecialchars($saved['subject']) . "</p>";
                    }
                } else {
                    $error = "❌ Failed to save message";
                    echo "<p style='color: red;'>$error</p>";
                }
            } catch (Exception $e) {
                $error = "❌ Error: " . $e->getMessage();
                echo "<p style='color: red;'>$error</p>";
            }
        }
    }
}

// Show current messages count
if ($conn) {
    try {
        $count_result = $conn->query("SELECT COUNT(*) as count FROM contact_messages");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo "<p><strong>Total messages in database: $count</strong></p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error checking message count: " . $e->getMessage() . "</p>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Simple Contact Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 15px 0; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        textarea { height: 100px; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #005a87; }
        .success { color: green; background: #f0f8f0; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .error { color: red; background: #f8f0f0; padding: 10px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <?php if ($message): ?>
        <div class="success"><?php echo $message; ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="error"><?php echo $error; ?></div>
    <?php endif; ?>
    
    <h2>Test Contact Form</h2>
    <form method="POST">
        <div class="form-group">
            <label for="name">Name *</label>
            <input type="text" id="name" name="name" required value="<?php echo htmlspecialchars($_POST['name'] ?? 'Test User'); ?>">
        </div>
        
        <div class="form-group">
            <label for="email">Email *</label>
            <input type="email" id="email" name="email" required value="<?php echo htmlspecialchars($_POST['email'] ?? '<EMAIL>'); ?>">
        </div>
        
        <div class="form-group">
            <label for="subject">Subject *</label>
            <input type="text" id="subject" name="subject" required value="<?php echo htmlspecialchars($_POST['subject'] ?? 'Test Message - ' . date('H:i:s')); ?>">
        </div>
        
        <div class="form-group">
            <label for="business_type">Business Type</label>
            <select id="business_type" name="business_type">
                <option value="">Select business type</option>
                <option value="Startup" <?php echo ($_POST['business_type'] ?? '') === 'Startup' ? 'selected' : ''; ?>>Startup</option>
                <option value="Small Business" <?php echo ($_POST['business_type'] ?? 'Small Business') === 'Small Business' ? 'selected' : ''; ?>>Small Business</option>
                <option value="Enterprise" <?php echo ($_POST['business_type'] ?? '') === 'Enterprise' ? 'selected' : ''; ?>>Enterprise</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="message">Message *</label>
            <textarea id="message" name="message" required><?php echo htmlspecialchars($_POST['message'] ?? 'This is a test message to verify the contact form is working properly.'); ?></textarea>
        </div>
        
        <button type="submit" name="submit_test" class="btn">Send Test Message</button>
    </form>
    
    <h3>Quick Links:</h3>
    <p>
        <a href="contact.php" class="btn">Real Contact Form</a>
        <a href="admin/messages.php" class="btn">Admin Messages</a>
        <a href="test-contact-database.php" class="btn">Database Test</a>
        <a href="debug-contact-messages.php" class="btn">Debug Messages</a>
    </p>
    
    <p style="color: red;"><strong>⚠️ Delete this file after debugging!</strong></p>
</body>
</html>
