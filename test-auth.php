<?php
// Test authentication system
require_once 'includes/db.php';

echo "<h1>Authentication System Test</h1>";

// Test database connection
if ($conn) {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    
    // Check if users table exists
    $result = $conn->query("SHOW TABLES LIKE 'users'");
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ Users table exists</p>";
        
        // Check table structure
        $structure = $conn->query("DESCRIBE users");
        if ($structure) {
            echo "<h3>Users Table Structure:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            while ($row = $structure->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "<td>" . $row['Default'] . "</td>";
                echo "<td>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Check if admin user exists
        $admin_check = $conn->query("SELECT * FROM users WHERE username = 'admin'");
        if ($admin_check && $admin_check->num_rows > 0) {
            echo "<p style='color: green;'>✅ Admin user exists</p>";
            $admin = $admin_check->fetch_assoc();
            echo "<p>Admin details: " . $admin['first_name'] . " " . $admin['last_name'] . " (" . $admin['email'] . ")</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ Admin user does not exist</p>";
        }
        
        // Count total users
        $count_result = $conn->query("SELECT COUNT(*) as total FROM users");
        if ($count_result) {
            $count = $count_result->fetch_assoc();
            echo "<p>Total users in database: " . $count['total'] . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Users table does not exist</p>";
        echo "<p><a href='admin/create-database.php'>Create Database & Tables</a></p>";
    }
    
    // Test helper functions
    echo "<h3>Testing Helper Functions:</h3>";
    
    // Test sanitizeInput
    $test_input = "<script>alert('test')</script>Test Input";
    $sanitized = sanitizeInput($test_input);
    echo "<p>sanitizeInput test: '" . $test_input . "' → '" . $sanitized . "'</p>";
    
    // Test validateEmail
    $test_emails = ['<EMAIL>', 'invalid-email', '<EMAIL>'];
    foreach ($test_emails as $email) {
        $valid = validateEmail($email) ? 'Valid' : 'Invalid';
        echo "<p>validateEmail test: '" . $email . "' → " . $valid . "</p>";
    }
    
    // Test password hashing
    $test_password = 'testpassword123';
    $hashed = hashPassword($test_password);
    $verified = verifyPassword($test_password, $hashed);
    echo "<p>Password hashing test: '" . $test_password . "' → " . ($verified ? 'Success' : 'Failed') . "</p>";
    
} else {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    echo "<p>Please run the database setup first:</p>";
    echo "<p><a href='admin/create-database.php'>Setup Database</a></p>";
}

// Test session functionality
session_start();
echo "<h3>Session Test:</h3>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session status: " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";

?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
a { color: #8b5cf6; text-decoration: none; padding: 10px 20px; background: #f0f9ff; border-radius: 5px; display: inline-block; margin: 5px 0; }
a:hover { background: #e0f2fe; }
</style>
