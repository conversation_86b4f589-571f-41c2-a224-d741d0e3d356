-- ===== SHIFTUR LIGHT THEME - DATABASE SETUP =====

-- Note: Database creation is handled by the installation script

-- Users table for admin panel
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    role ENUM('admin', 'editor', 'viewer') DEFAULT 'viewer',
    status ENUM('active', 'inactive') DEFAULT 'active',
    last_login DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Services table
CREATE TABLE services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    short_description VARCHAR(255) NOT NULL,
    icon VARCHAR(50) NOT NULL,
    features TEXT,
    price_range VARCHAR(50),
    display_order INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    meta_title VARCHAR(100),
    meta_description VARCHAR(160),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Portfolio table
CREATE TABLE portfolio (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT NOT NULL,
    short_description VARCHAR(255) NOT NULL,
    category ENUM('web', 'app', 'design', 'ai', 'marketing') NOT NULL,
    technologies TEXT,
    client_name VARCHAR(100),
    project_url VARCHAR(255),
    image_url VARCHAR(255),
    gallery TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    featured BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 0,
    completed_at DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Testimonials table
CREATE TABLE testimonials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_name VARCHAR(100) NOT NULL,
    client_position VARCHAR(100),
    client_company VARCHAR(100),
    client_image VARCHAR(255),
    testimonial TEXT NOT NULL,
    rating INT DEFAULT 5 CHECK (rating >= 1 AND rating <= 5),
    status ENUM('active', 'inactive') DEFAULT 'active',
    featured BOOLEAN DEFAULT FALSE,
    display_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    company VARCHAR(100),
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    service_interest VARCHAR(100),
    business_type VARCHAR(50),
    status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Blog posts table
CREATE TABLE blog_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(200) UNIQUE NOT NULL,
    excerpt TEXT,
    content LONGTEXT NOT NULL,
    featured_image VARCHAR(255),
    category VARCHAR(50),
    tags TEXT,
    author_id INT,
    status ENUM('draft', 'published', 'archived') DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    views INT DEFAULT 0,
    published_at DATETIME,
    meta_title VARCHAR(100),
    meta_description VARCHAR(160),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Settings table
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('text', 'textarea', 'number', 'boolean', 'json') DEFAULT 'text',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Activity logs table
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Newsletter subscribers table
CREATE TABLE newsletter_subscribers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(100),
    status ENUM('active', 'unsubscribed') DEFAULT 'active',
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unsubscribed_at TIMESTAMP NULL
);

-- Insert default admin user
INSERT INTO users (username, email, password, first_name, last_name, role, status) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'admin', 'active');
-- Default password is 'password' - change this immediately after setup

-- Insert default services
INSERT INTO services (title, slug, description, short_description, icon, features, display_order, status) VALUES
('Web & App Development', 'web-app-development', 'Custom websites and mobile applications built with modern technologies for optimal performance and user experience.', 'Custom websites and mobile applications built with modern technologies.', 'fas fa-code', 'Responsive Web Design, Mobile App Development, E-commerce Solutions, Custom Web Applications, API Development, Database Design', 1, 'active'),

('Amazon Services', 'amazon-services', 'Complete Amazon marketplace solutions including store setup, optimization, and management for maximum sales growth.', 'Complete Amazon marketplace solutions for maximum sales growth.', 'fab fa-amazon', 'Amazon Store Setup, Product Listing Optimization, PPC Campaign Management, Inventory Management, Brand Registry, A+ Content Creation', 2, 'active'),

('Digital Marketing', 'digital-marketing', 'Strategic digital marketing campaigns to increase your online presence and drive qualified leads to your business.', 'Strategic marketing campaigns to increase your online presence.', 'fas fa-bullhorn', 'Social Media Marketing, Search Engine Optimization, Pay-Per-Click Advertising, Content Marketing, Email Marketing, Analytics & Reporting', 3, 'active'),

('AI Business Solutions', 'ai-business-solutions', 'Leverage artificial intelligence to automate processes, gain insights, and enhance your business operations.', 'Leverage AI to automate processes and enhance operations.', 'fas fa-robot', 'Process Automation, Predictive Analytics, AI Chatbots, Business Intelligence, Machine Learning, Data Analysis', 4, 'active'),

('Graphic Design', 'graphic-design', 'Creative design solutions that communicate your brand message effectively and leave a lasting impression.', 'Creative design solutions that communicate your brand effectively.', 'fas fa-palette', 'Brand Identity Design, Marketing Materials, Web Graphics, Print Design, Logo Design, Business Cards', 5, 'active');

-- Insert sample portfolio items
INSERT INTO portfolio (title, slug, description, short_description, category, technologies, client_name, status, featured, display_order) VALUES
('E-Commerce Platform', 'ecommerce-platform', 'A comprehensive e-commerce solution featuring secure payment processing, advanced product filtering, user account management, and admin dashboard.', 'Modern online store with advanced features and payment integration.', 'web', 'PHP, MySQL, JavaScript, Bootstrap, PayPal API', 'TechStore Inc.', 'active', TRUE, 1),

('Mobile Banking App', 'mobile-banking-app', 'Secure mobile banking application featuring biometric authentication, real-time transaction monitoring, bill payments, and comprehensive financial management tools.', 'Secure banking app with biometric authentication and real-time transactions.', 'app', 'React Native, Node.js, MongoDB, Firebase, Biometric API', 'SecureBank', 'active', TRUE, 2),

('Brand Identity Package', 'brand-identity-package', 'Complete brand identity design for tech startup including logo design, brand guidelines, business cards, letterheads, and digital assets.', 'Complete brand identity design including logo and guidelines.', 'design', 'Adobe Illustrator, Adobe Photoshop, Brand Guidelines, Print Design', 'InnovateTech', 'active', FALSE, 3);

-- Insert sample testimonials
INSERT INTO testimonials (client_name, client_position, client_company, testimonial, rating, status, featured, display_order) VALUES
('John Smith', 'CEO', 'TechStart Inc.', 'Shiftur transformed our business with their innovative web development solutions. The team was professional, responsive, and delivered beyond our expectations.', 5, 'active', TRUE, 1),

('Sarah Johnson', 'Marketing Director', 'GrowthCorp', 'Their digital marketing strategies helped us increase our online presence by 300%. Highly recommend their services for any business looking to grow.', 5, 'active', TRUE, 2),

('Michael Brown', 'Founder', 'E-Shop Pro', 'The e-commerce platform they built for us has been a game-changer. Sales have increased significantly, and the user experience is fantastic.', 5, 'active', FALSE, 3);

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES
('site_title', 'Shiftur - Digital Solutions & Business Growth', 'text', 'Main site title'),
('site_description', 'Transform your business with our comprehensive digital solutions', 'textarea', 'Site description for SEO'),
('contact_email', '<EMAIL>', 'text', 'Main contact email'),
('contact_phone', '+****************', 'text', 'Main contact phone'),
('contact_address', '2540 Old Court Road, Pikesville, Baltimore, Maryland, USA', 'textarea', 'Business address'),
('social_facebook', '#', 'text', 'Facebook page URL'),
('social_twitter', '#', 'text', 'Twitter profile URL'),
('social_linkedin', '#', 'text', 'LinkedIn profile URL'),
('social_instagram', '#', 'text', 'Instagram profile URL'),
('google_analytics', '', 'text', 'Google Analytics tracking ID'),
('maintenance_mode', 'false', 'boolean', 'Enable maintenance mode');

-- Create indexes for better performance
CREATE INDEX idx_services_status ON services(status);
CREATE INDEX idx_services_display_order ON services(display_order);
CREATE INDEX idx_portfolio_category ON portfolio(category);
CREATE INDEX idx_portfolio_status ON portfolio(status);
CREATE INDEX idx_portfolio_featured ON portfolio(featured);
CREATE INDEX idx_testimonials_status ON testimonials(status);
CREATE INDEX idx_testimonials_featured ON testimonials(featured);
CREATE INDEX idx_contact_messages_status ON contact_messages(status);
CREATE INDEX idx_blog_posts_status ON blog_posts(status);
CREATE INDEX idx_blog_posts_published_at ON blog_posts(published_at);
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);
