<?php
// Setup script for live database on shiftur.co
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Live Database Setup for shiftur.co</h1>";

// Include database connection
try {
    require_once 'includes/db.php';
    echo "<p style='color: green;'>✅ Database connection file loaded</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error loading database file: " . $e->getMessage() . "</p>";
    exit;
}

// Check database connection
if (!$conn) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    echo "<p>Please check your database credentials in includes/db.php</p>";
    exit;
}

echo "<p style='color: green;'>✅ Connected to database: " . htmlspecialchars($db_name) . "</p>";

// Function to create tables
function createTable($conn, $tableName, $sql, $description) {
    echo "<h3>Creating $description...</h3>";
    
    // Check if table already exists
    $check = $conn->query("SHOW TABLES LIKE '$tableName'");
    if ($check && $check->num_rows > 0) {
        echo "<p style='color: orange;'>⚠️ Table '$tableName' already exists</p>";
        return true;
    }
    
    // Create table
    if ($conn->query($sql)) {
        echo "<p style='color: green;'>✅ Table '$tableName' created successfully</p>";
        return true;
    } else {
        echo "<p style='color: red;'>❌ Error creating table '$tableName': " . $conn->error . "</p>";
        return false;
    }
}

// Create website_users table
$website_users_sql = "
CREATE TABLE website_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    phone VARCHAR(20),
    company VARCHAR(100),
    status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_token_expires TIMESTAMP NULL,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

createTable($conn, 'website_users', $website_users_sql, 'Website Users Table');

// Create admin_users table
$admin_users_sql = "
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    role ENUM('super_admin', 'admin', 'editor', 'viewer') DEFAULT 'admin',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

createTable($conn, 'admin_users', $admin_users_sql, 'Admin Users Table');

// Create contact_messages table (if it doesn't exist)
$contact_messages_sql = "
CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    company VARCHAR(100),
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    service_interest VARCHAR(100),
    business_type VARCHAR(50),
    ip_address VARCHAR(45),
    user_agent TEXT,
    status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)";

createTable($conn, 'contact_messages', $contact_messages_sql, 'Contact Messages Table');

// Create default admin user
echo "<h3>Creating Default Admin User...</h3>";

try {
    // Check if admin user already exists
    $admin_check = $conn->query("SELECT COUNT(*) as count FROM admin_users");
    if ($admin_check) {
        $admin_count = $admin_check->fetch_assoc()['count'];
        
        if ($admin_count == 0) {
            // Create default admin user
            require_once 'includes/admin-auth.php';
            
            $default_admin = [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => 'admin123',  // Change this password after first login!
                'first_name' => 'Admin',
                'last_name' => 'User',
                'role' => 'super_admin',
                'status' => 'active'
            ];
            
            $result = createAdminUser($default_admin);
            if ($result['success']) {
                echo "<p style='color: green;'>✅ Default admin user created</p>";
                echo "<p><strong>Admin Login:</strong></p>";
                echo "<p>Email: <EMAIL></p>";
                echo "<p>Password: admin123</p>";
                echo "<p style='color: red;'><strong>⚠️ IMPORTANT: Change this password after first login!</strong></p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to create admin user: " . $result['message'] . "</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ Admin users already exist ($admin_count users)</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error creating admin user: " . $e->getMessage() . "</p>";
}

// Test account creation
echo "<h3>Testing Account Creation...</h3>";

if ($_POST && isset($_POST['test_create'])) {
    $test_username = 'testuser_' . time();
    $test_email = 'test_' . time() . '@example.com';
    
    try {
        require_once 'includes/website-auth.php';
        
        $test_data = [
            'username' => $test_username,
            'email' => $test_email,
            'password' => 'TestPassword123',
            'first_name' => 'Test',
            'last_name' => 'User',
            'status' => 'active'
        ];
        
        $result = createWebsiteUser($test_data);
        
        if ($result && $result['success']) {
            echo "<p style='color: green;'>✅ Test account creation successful! User ID: " . $result['user_id'] . "</p>";
            
            // Clean up test user
            $conn->query("DELETE FROM website_users WHERE id = " . $result['user_id']);
            echo "<p>Test user cleaned up</p>";
        } else {
            $message = isset($result['message']) ? $result['message'] : 'Unknown error';
            echo "<p style='color: red;'>❌ Test account creation failed: " . htmlspecialchars($message) . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error during test: " . $e->getMessage() . "</p>";
    }
}

// Show current database status
echo "<h3>Database Status</h3>";

$tables = ['website_users', 'admin_users', 'contact_messages'];
foreach ($tables as $table) {
    try {
        $check = $conn->query("SHOW TABLES LIKE '$table'");
        if ($check && $check->num_rows > 0) {
            $count_result = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_result ? $count_result->fetch_assoc()['count'] : 0;
            echo "<p style='color: green;'>✅ $table: $count records</p>";
        } else {
            echo "<p style='color: red;'>❌ $table: Table missing</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ $table: Error - " . $e->getMessage() . "</p>";
    }
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Live Database Setup - shiftur.co</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 5px; }
        .btn:hover { background: #005a87; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="warning">
        <h3>⚠️ Security Notice</h3>
        <p>This setup script should be deleted after use for security reasons.</p>
        <p>Make sure to change the default admin password after first login.</p>
    </div>
    
    <h3>Test Account Creation</h3>
    <form method="POST">
        <button type="submit" name="test_create" class="btn">Test Account Creation</button>
    </form>
    
    <h3>Next Steps</h3>
    <ol>
        <li>✅ Database tables are now set up</li>
        <li>🔄 Test the signup form: <a href="signup.php">signup.php</a></li>
        <li>🔄 Test the login form: <a href="login.php">login.php</a></li>
        <li>🔄 Access admin panel: <a href="admin/login.php">admin/login.php</a></li>
        <li>⚠️ Delete this setup file after testing</li>
        <li>⚠️ Change admin password after first login</li>
    </ol>
    
    <div class="success">
        <h3>✅ Setup Complete!</h3>
        <p>Your live website database is now configured for account creation.</p>
        <p>Users should now be able to create accounts on shiftur.co</p>
    </div>
</body>
</html>
