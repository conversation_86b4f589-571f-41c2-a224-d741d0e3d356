<?php
// Website user authentication functions
require_once 'db.php';

// Website user authentication functions
function authenticateWebsiteUser($username, $password) {
    global $conn;
    if (!$conn) return false;
    
    $user = fetchOne($conn, "SELECT * FROM website_users WHERE (username = ? OR email = ?) AND status = 'active'", [$username, $username]);
    
    if ($user && verifyPassword($password, $user['password'])) {
        return $user;
    }
    
    return false;
}

function createWebsiteUser($data) {
    global $conn;
    if (!$conn) return false;
    
    // Check if username or email already exists
    $existing = fetchOne($conn, "SELECT id FROM website_users WHERE username = ? OR email = ?", [$data['username'], $data['email']]);
    if ($existing) {
        return ['success' => false, 'message' => 'Username or email already exists'];
    }
    
    // Hash password
    $data['password'] = hashPassword($data['password']);
    
    // Set default values
    $data['status'] = $data['status'] ?? 'active';
    $data['email_verified'] = $data['email_verified'] ?? false;
    
    $userId = insertRecord($conn, 'website_users', $data);
    
    if ($userId) {
        logWebsiteActivity($userId, 'User registered', 'New user account created');
        return ['success' => true, 'user_id' => $userId];
    }
    
    return ['success' => false, 'message' => 'Failed to create user account'];
}

function updateWebsiteUser($userId, $data) {
    global $conn;
    if (!$conn) return false;
    
    // Don't allow updating password through this function
    unset($data['password']);
    
    $result = updateRecord($conn, 'website_users', $data, 'id = ?', [$userId]);
    
    if ($result) {
        logWebsiteActivity($userId, 'Profile updated', 'User profile information updated');
        return true;
    }
    
    return false;
}

function changeWebsiteUserPassword($userId, $currentPassword, $newPassword) {
    global $conn;
    if (!$conn) return false;
    
    // Verify current password
    $user = fetchOne($conn, "SELECT password FROM website_users WHERE id = ?", [$userId]);
    if (!$user || !verifyPassword($currentPassword, $user['password'])) {
        return ['success' => false, 'message' => 'Current password is incorrect'];
    }
    
    // Update password
    $hashedPassword = hashPassword($newPassword);
    $result = updateRecord($conn, 'website_users', ['password' => $hashedPassword], 'id = ?', [$userId]);
    
    if ($result) {
        logWebsiteActivity($userId, 'Password changed', 'User changed their password');
        return ['success' => true, 'message' => 'Password changed successfully'];
    }
    
    return ['success' => false, 'message' => 'Failed to change password'];
}

function getWebsiteUser($userId) {
    global $conn;
    if (!$conn) return null;
    
    return fetchOne($conn, "SELECT * FROM website_users WHERE id = ?", [$userId]);
}

function getWebsiteUserByUsername($username) {
    global $conn;
    if (!$conn) return null;
    
    return fetchOne($conn, "SELECT * FROM website_users WHERE username = ? OR email = ?", [$username, $username]);
}

function logWebsiteActivity($userId, $action, $details = '') {
    global $conn;
    if (!$conn) return false;
    
    $data = [
        'website_user_id' => $userId,
        'action' => $action,
        'details' => $details,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
    ];
    
    return insertRecord($conn, 'website_activity_logs', $data);
}

function getWebsiteUserActivity($userId, $limit = 10) {
    global $conn;
    if (!$conn) return [];
    
    return fetchAll($conn, "SELECT * FROM website_activity_logs WHERE website_user_id = ? ORDER BY created_at DESC LIMIT ?", [$userId, $limit]);
}

function loginWebsiteUser($user) {
    // Set session variables for website user
    $_SESSION['website_user_id'] = $user['id'];
    $_SESSION['website_user_name'] = $user['first_name'] . ' ' . $user['last_name'];
    $_SESSION['website_username'] = $user['username'];
    $_SESSION['website_user_email'] = $user['email'];
    $_SESSION['website_user_status'] = $user['status'];
    
    // Update last login
    global $conn;
    if ($conn) {
        updateRecord($conn, 'website_users', ['last_login' => date('Y-m-d H:i:s')], 'id = ?', [$user['id']]);
        logWebsiteActivity($user['id'], 'User logged in', 'Login from IP: ' . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown'));
    }
    
    return true;
}

function logoutWebsiteUser() {
    if (isset($_SESSION['website_user_id'])) {
        logWebsiteActivity($_SESSION['website_user_id'], 'User logged out', 'Logout from IP: ' . ($_SERVER['REMOTE_ADDR'] ?? 'Unknown'));
    }
    
    // Clear website user session variables
    unset($_SESSION['website_user_id']);
    unset($_SESSION['website_user_name']);
    unset($_SESSION['website_username']);
    unset($_SESSION['website_user_email']);
    unset($_SESSION['website_user_status']);
    
    return true;
}

function isWebsiteUserLoggedIn() {
    return isset($_SESSION['website_user_id']);
}

function requireWebsiteLogin() {
    if (!isWebsiteUserLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

function generatePasswordResetToken($email) {
    global $conn;
    if (!$conn) return false;
    
    $user = fetchOne($conn, "SELECT id FROM website_users WHERE email = ? AND status = 'active'", [$email]);
    if (!$user) {
        return ['success' => false, 'message' => 'Email address not found'];
    }
    
    $token = generateToken(32);
    $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));
    
    $result = updateRecord($conn, 'website_users', [
        'reset_token' => $token,
        'reset_token_expires' => $expires
    ], 'id = ?', [$user['id']]);
    
    if ($result) {
        logWebsiteActivity($user['id'], 'Password reset requested', 'Password reset token generated');
        return ['success' => true, 'token' => $token];
    }
    
    return ['success' => false, 'message' => 'Failed to generate reset token'];
}

function resetPasswordWithToken($token, $newPassword) {
    global $conn;
    if (!$conn) return false;
    
    $user = fetchOne($conn, "SELECT id FROM website_users WHERE reset_token = ? AND reset_token_expires > NOW() AND status = 'active'", [$token]);
    if (!$user) {
        return ['success' => false, 'message' => 'Invalid or expired reset token'];
    }
    
    $hashedPassword = hashPassword($newPassword);
    $result = updateRecord($conn, 'website_users', [
        'password' => $hashedPassword,
        'reset_token' => null,
        'reset_token_expires' => null
    ], 'id = ?', [$user['id']]);
    
    if ($result) {
        logWebsiteActivity($user['id'], 'Password reset completed', 'Password reset using token');
        return ['success' => true, 'message' => 'Password reset successfully'];
    }
    
    return ['success' => false, 'message' => 'Failed to reset password'];
}

function verifyEmail($token) {
    global $conn;
    if (!$conn) return false;
    
    $user = fetchOne($conn, "SELECT id FROM website_users WHERE verification_token = ? AND status = 'pending'", [$token]);
    if (!$user) {
        return ['success' => false, 'message' => 'Invalid verification token'];
    }
    
    $result = updateRecord($conn, 'website_users', [
        'email_verified' => true,
        'status' => 'active',
        'verification_token' => null
    ], 'id = ?', [$user['id']]);
    
    if ($result) {
        logWebsiteActivity($user['id'], 'Email verified', 'User verified their email address');
        return ['success' => true, 'message' => 'Email verified successfully'];
    }
    
    return ['success' => false, 'message' => 'Failed to verify email'];
}
?>
