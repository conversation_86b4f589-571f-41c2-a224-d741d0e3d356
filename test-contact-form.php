<?php
// Test contact form functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Contact Form Test</h1>";

// Include database
require_once 'includes/db.php';

if (!$conn) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    exit;
}

$message = '';

// Handle form submission
if ($_POST && isset($_POST['submit_contact'])) {
    echo "<h2>Processing Contact Form...</h2>";
    
    $name = sanitizeInput($_POST['name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $company = sanitizeInput($_POST['company'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $message_text = sanitizeInput($_POST['message'] ?? '');
    $service_interest = sanitizeInput($_POST['service_interest'] ?? '');
    $business_type = sanitizeInput($_POST['business_type'] ?? '');
    
    echo "<p>Name: " . htmlspecialchars($name) . "</p>";
    echo "<p>Email: " . htmlspecialchars($email) . "</p>";
    echo "<p>Subject: " . htmlspecialchars($subject) . "</p>";
    
    // Validation
    if (empty($name) || empty($email) || empty($subject) || empty($message_text)) {
        $message = '<p style="color: red;">Please fill in all required fields.</p>';
    } elseif (!validateEmail($email)) {
        $message = '<p style="color: red;">Please enter a valid email address.</p>';
    } else {
        // Save to database
        echo "<p>Saving to database...</p>";
        
        $data = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'company' => $company,
            'subject' => $subject,
            'message' => $message_text,
            'service_interest' => $service_interest,
            'business_type' => $business_type,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'status' => 'new'
        ];
        
        try {
            $result = insertRecord($conn, 'contact_messages', $data);
            if ($result) {
                $message = '<p style="color: green;">✅ Message saved successfully! ID: ' . $result . '</p>';
                echo "<p style='color: green;'>✅ Contact message saved with ID: $result</p>";
                
                // Verify it was saved
                $saved_message = fetchOne($conn, "SELECT * FROM contact_messages WHERE id = ?", [$result]);
                if ($saved_message) {
                    echo "<p style='color: green;'>✅ Message verified in database</p>";
                    echo "<p>Saved data: " . htmlspecialchars($saved_message['name']) . " - " . htmlspecialchars($saved_message['subject']) . "</p>";
                } else {
                    echo "<p style='color: red;'>❌ Message not found after saving</p>";
                }
            } else {
                $message = '<p style="color: red;">❌ Failed to save message</p>';
                echo "<p style='color: red;'>❌ insertRecord returned false</p>";
            }
        } catch (Exception $e) {
            $message = '<p style="color: red;">❌ Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
            echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
        }
    }
}

// Show current messages count
try {
    $count_result = $conn->query("SELECT COUNT(*) as count FROM contact_messages");
    if ($count_result) {
        $count = $count_result->fetch_assoc()['count'];
        echo "<p>Current messages in database: <strong>$count</strong></p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting count: " . $e->getMessage() . "</p>";
}

// Show recent messages
try {
    $recent_messages = fetchAll($conn, "SELECT id, name, email, subject, created_at, status FROM contact_messages ORDER BY created_at DESC LIMIT 5");
    if (!empty($recent_messages)) {
        echo "<h3>Recent Messages:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Subject</th><th>Status</th><th>Created</th></tr>";
        foreach ($recent_messages as $msg) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($msg['id']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['name']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['email']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['subject']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['status'] ?? 'new') . "</td>";
            echo "<td>" . htmlspecialchars($msg['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No messages found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting messages: " . $e->getMessage() . "</p>";
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Contact Form Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group textarea, .form-group select { 
            width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; 
            box-sizing: border-box;
        }
        .form-group textarea { height: 100px; resize: vertical; }
        .btn { padding: 12px 24px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; }
        .btn:hover { background: #005a87; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .contact-form { background: #f9f9f9; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <?php echo $message; ?>
    
    <div class="contact-form">
        <h2>Test Contact Form</h2>
        <form method="POST">
            <div class="form-group">
                <label for="name">Full Name *</label>
                <input type="text" id="name" name="name" required 
                       value="<?php echo htmlspecialchars($_POST['name'] ?? 'John Doe'); ?>">
            </div>
            
            <div class="form-group">
                <label for="email">Email Address *</label>
                <input type="email" id="email" name="email" required 
                       value="<?php echo htmlspecialchars($_POST['email'] ?? '<EMAIL>'); ?>">
            </div>
            
            <div class="form-group">
                <label for="phone">Phone Number</label>
                <input type="tel" id="phone" name="phone" 
                       value="<?php echo htmlspecialchars($_POST['phone'] ?? '******-0123'); ?>">
            </div>
            
            <div class="form-group">
                <label for="company">Company</label>
                <input type="text" id="company" name="company" 
                       value="<?php echo htmlspecialchars($_POST['company'] ?? 'Test Company'); ?>">
            </div>
            
            <div class="form-group">
                <label for="subject">Subject *</label>
                <input type="text" id="subject" name="subject" required 
                       value="<?php echo htmlspecialchars($_POST['subject'] ?? 'Test Contact Message'); ?>">
            </div>
            
            <div class="form-group">
                <label for="service_interest">Service Interest</label>
                <select id="service_interest" name="service_interest">
                    <option value="">Select a service</option>
                    <option value="Web Development" <?php echo ($_POST['service_interest'] ?? '') === 'Web Development' ? 'selected' : ''; ?>>Web Development</option>
                    <option value="Digital Marketing" <?php echo ($_POST['service_interest'] ?? '') === 'Digital Marketing' ? 'selected' : ''; ?>>Digital Marketing</option>
                    <option value="Graphic Design" <?php echo ($_POST['service_interest'] ?? '') === 'Graphic Design' ? 'selected' : ''; ?>>Graphic Design</option>
                    <option value="AI Solutions" <?php echo ($_POST['service_interest'] ?? '') === 'AI Solutions' ? 'selected' : ''; ?>>AI Solutions</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="business_type">Business Type</label>
                <select id="business_type" name="business_type">
                    <option value="">Select business type</option>
                    <option value="Startup" <?php echo ($_POST['business_type'] ?? '') === 'Startup' ? 'selected' : ''; ?>>Startup</option>
                    <option value="Small Business" <?php echo ($_POST['business_type'] ?? '') === 'Small Business' ? 'selected' : ''; ?>>Small Business</option>
                    <option value="Enterprise" <?php echo ($_POST['business_type'] ?? '') === 'Enterprise' ? 'selected' : ''; ?>>Enterprise</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="message">Message *</label>
                <textarea id="message" name="message" required><?php echo htmlspecialchars($_POST['message'] ?? 'This is a test message to verify the contact form is working properly and messages are being saved to the database.'); ?></textarea>
            </div>
            
            <button type="submit" name="submit_contact" class="btn">Send Test Message</button>
        </form>
    </div>
    
    <h2>Quick Links</h2>
    <p>
        <a href="contact.php">Real Contact Form</a> | 
        <a href="admin/messages.php">Admin Messages</a> | 
        <a href="admin/dashboard.php">Admin Dashboard</a> | 
        <a href="debug-contact-messages.php">Full Debug</a>
    </p>
    
    <h2>Testing Steps</h2>
    <ol>
        <li>✅ Fill out the form above and submit</li>
        <li>🔄 Check if message appears in the table below</li>
        <li>🔄 Go to admin messages page to see if it shows there</li>
        <li>🔄 Test the real contact form on your website</li>
    </ol>
    
    <p style="color: red;"><strong>⚠️ Delete this test file after debugging!</strong></p>
</body>
</html>
