<?php
// Test contact database functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Contact Database Test</h1>";

require_once 'includes/db.php';

if (!$conn) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    exit;
}

echo "<p style='color: green;'>✅ Database connected</p>";

// Check if contact_messages table exists
echo "<h2>1. Table Structure Check</h2>";
try {
    $table_check = $conn->query("SHOW TABLES LIKE 'contact_messages'");
    if ($table_check && $table_check->num_rows > 0) {
        echo "<p style='color: green;'>✅ contact_messages table exists</p>";
        
        // Show table structure
        $columns = $conn->query("SHOW COLUMNS FROM contact_messages");
        if ($columns) {
            echo "<h3>Table Structure:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($row = $columns->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<p style='color: red;'>❌ contact_messages table does not exist</p>";
        echo "<p>Creating table...</p>";
        
        $create_sql = "
        CREATE TABLE contact_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            company VARCHAR(100),
            subject VARCHAR(200) NOT NULL,
            message TEXT NOT NULL,
            service_interest VARCHAR(100),
            business_type VARCHAR(50),
            ip_address VARCHAR(45),
            user_agent TEXT,
            status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if ($conn->query($create_sql)) {
            echo "<p style='color: green;'>✅ Table created successfully</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create table: " . $conn->error . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking table: " . $e->getMessage() . "</p>";
}

// Check current data
echo "<h2>2. Current Data Check</h2>";
try {
    $count_result = $conn->query("SELECT COUNT(*) as count FROM contact_messages");
    if ($count_result) {
        $count = $count_result->fetch_assoc()['count'];
        echo "<p>Total messages in database: <strong>$count</strong></p>";
        
        if ($count > 0) {
            $recent = $conn->query("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 3");
            if ($recent) {
                echo "<h3>Recent Messages:</h3>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Subject</th><th>Business Type</th><th>Status</th><th>Created</th></tr>";
                while ($row = $recent->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['email']) . "</td>";
                    echo "<td>" . htmlspecialchars(substr($row['subject'], 0, 30)) . "...</td>";
                    echo "<td>" . htmlspecialchars($row['business_type'] ?? 'N/A') . "</td>";
                    echo "<td>" . htmlspecialchars($row['status']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking data: " . $e->getMessage() . "</p>";
}

// Test insert functionality
echo "<h2>3. Insert Test</h2>";
if ($_POST && isset($_POST['test_insert'])) {
    echo "<p>Testing insert functionality...</p>";
    
    $test_data = [
        'name' => 'Test User ' . date('H:i:s'),
        'email' => 'test' . time() . '@example.com',
        'phone' => '******-0123',
        'company' => 'Test Company',
        'subject' => 'Test Message - ' . date('Y-m-d H:i:s'),
        'message' => 'This is a test message to verify the contact form database functionality.',
        'service_interest' => 'Web Development',
        'business_type' => 'Small Business',
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Test Script',
        'status' => 'new'
    ];
    
    try {
        $result = insertRecord($conn, 'contact_messages', $test_data);
        if ($result) {
            echo "<p style='color: green;'>✅ Test message inserted successfully! ID: $result</p>";
            
            // Verify the insert
            $verify = fetchOne($conn, "SELECT * FROM contact_messages WHERE id = ?", [$result]);
            if ($verify) {
                echo "<p style='color: green;'>✅ Message verified in database</p>";
                echo "<p>Inserted data: " . htmlspecialchars($verify['name']) . " - " . htmlspecialchars($verify['subject']) . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Failed to insert test message</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Insert error: " . $e->getMessage() . "</p>";
    }
}

// Test the exact contact form processing
echo "<h2>4. Contact Form Processing Test</h2>";
if ($_POST && isset($_POST['test_contact_form'])) {
    echo "<p>Testing contact form processing...</p>";
    
    // Simulate contact form data
    $_POST['name'] = 'Contact Form Test';
    $_POST['email'] = '<EMAIL>';
    $_POST['subject'] = 'Contact Form Test - ' . date('H:i:s');
    $_POST['message'] = 'Testing contact form processing functionality';
    $_POST['business_type'] = 'Startup';
    $_POST['submit_contact'] = true;
    
    // Include the contact form processing logic
    include_once 'includes/functions.php';
    
    $name = sanitizeInput($_POST['name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $company = sanitizeInput($_POST['company'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $message = sanitizeInput($_POST['message'] ?? '');
    $service_interest = sanitizeInput($_POST['service_interest'] ?? '');
    $business_type = sanitizeInput($_POST['business_type'] ?? '');
    
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        echo "<p style='color: red;'>❌ Validation failed: Missing required fields</p>";
    } elseif (!validateEmail($email)) {
        echo "<p style='color: red;'>❌ Validation failed: Invalid email</p>";
    } else {
        $data = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'company' => $company,
            'subject' => $subject,
            'message' => $message,
            'service_interest' => $service_interest,
            'business_type' => $business_type,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'status' => 'new'
        ];
        
        $result = insertRecord($conn, 'contact_messages', $data);
        if ($result) {
            echo "<p style='color: green;'>✅ Contact form processing successful! ID: $result</p>";
        } else {
            echo "<p style='color: red;'>❌ Contact form processing failed</p>";
        }
    }
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Contact Database Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 5px; }
        .btn:hover { background: #005a87; }
    </style>
</head>
<body>
    <h2>Test Actions</h2>
    
    <form method="POST" style="margin: 20px 0;">
        <button type="submit" name="test_insert" class="btn">Test Direct Insert</button>
        <p>This will test the insertRecord function directly</p>
    </form>
    
    <form method="POST" style="margin: 20px 0;">
        <button type="submit" name="test_contact_form" class="btn">Test Contact Form Processing</button>
        <p>This will test the exact same logic used in contact.php</p>
    </form>
    
    <h3>Quick Links:</h3>
    <p>
        <a href="contact.php" class="btn">Contact Form</a>
        <a href="admin/messages.php" class="btn">Admin Messages</a>
        <a href="test-contact-form.php" class="btn">Test Form</a>
    </p>
    
    <p style="color: red;"><strong>⚠️ Delete this file after debugging!</strong></p>
</body>
</html>
