<?php
// Setup authentication system
$db_host = 'localhost';
$db_username = 'root';
$db_password = '';
$db_name = 'shiftur_light';

$messages = [];
$success = false;

if ($_POST && isset($_POST['setup_auth'])) {
    try {
        // Connect to MySQL server (without database)
        $conn = new mysqli($db_host, $db_username, $db_password);
        
        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }
        
        $messages[] = "✅ Connected to MySQL server";
        
        // Create database if it doesn't exist
        $sql = "CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        if ($conn->query($sql) === TRUE) {
            $messages[] = "✅ Database '$db_name' created or already exists";
        } else {
            throw new Exception("Error creating database: " . $conn->error);
        }
        
        // Select the database
        $conn->select_db($db_name);
        $messages[] = "✅ Selected database '$db_name'";
        
        // Create users table
        $users_table = "
        CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            role ENUM('admin', 'editor', 'viewer') DEFAULT 'viewer',
            status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
            last_login DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($users_table) === TRUE) {
            $messages[] = "✅ Users table created or already exists";
        } else {
            throw new Exception("Error creating users table: " . $conn->error);
        }
        
        // Create activity_logs table
        $activity_table = "
        CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            action VARCHAR(255) NOT NULL,
            details TEXT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($activity_table) === TRUE) {
            $messages[] = "✅ Activity logs table created or already exists";
        } else {
            throw new Exception("Error creating activity_logs table: " . $conn->error);
        }
        
        // Check if admin user exists
        $admin_check = $conn->query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
        if ($admin_check) {
            $admin_count = $admin_check->fetch_assoc()['count'];
            if ($admin_count > 0) {
                $messages[] = "✅ Admin user already exists";
            } else {
                // Create admin user
                $password_hash = password_hash('password', PASSWORD_DEFAULT);
                $admin_sql = "INSERT INTO users (username, email, password, first_name, last_name, role, status) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($admin_sql);
                $stmt->bind_param('sssssss', 
                    $username, $email, $password_hash, $first_name, $last_name, $role, $status
                );
                
                $username = 'admin';
                $email = '<EMAIL>';
                $first_name = 'Admin';
                $last_name = 'User';
                $role = 'admin';
                $status = 'active';
                
                if ($stmt->execute()) {
                    $messages[] = "✅ Admin user created successfully";
                    $messages[] = "📧 Username: admin";
                    $messages[] = "🔑 Password: password";
                } else {
                    $messages[] = "❌ Failed to create admin user: " . $stmt->error;
                }
                $stmt->close();
            }
        }
        
        // Create a test regular user
        $test_check = $conn->query("SELECT COUNT(*) as count FROM users WHERE username = 'testuser'");
        if ($test_check) {
            $test_count = $test_check->fetch_assoc()['count'];
            if ($test_count == 0) {
                $password_hash = password_hash('testpass', PASSWORD_DEFAULT);
                $test_sql = "INSERT INTO users (username, email, password, first_name, last_name, role, status) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($test_sql);
                $stmt->bind_param('sssssss', 
                    $username, $email, $password_hash, $first_name, $last_name, $role, $status
                );
                
                $username = 'testuser';
                $email = '<EMAIL>';
                $first_name = 'Test';
                $last_name = 'User';
                $role = 'viewer';
                $status = 'active';
                
                if ($stmt->execute()) {
                    $messages[] = "✅ Test user created successfully";
                    $messages[] = "📧 Username: testuser";
                    $messages[] = "🔑 Password: testpass";
                } else {
                    $messages[] = "❌ Failed to create test user: " . $stmt->error;
                }
                $stmt->close();
            }
        }
        
        $success = true;
        $conn->close();
        
    } catch (Exception $e) {
        $messages[] = "❌ Error: " . $e->getMessage();
    }
}

// Test current connection
$current_status = [];
try {
    $test_conn = new mysqli($db_host, $db_username, $db_password, $db_name);
    if ($test_conn->connect_error) {
        $current_status['connection'] = "❌ Failed: " . $test_conn->connect_error;
    } else {
        $current_status['connection'] = "✅ Connected";
        
        // Check if users table exists
        $result = $test_conn->query("SHOW TABLES LIKE 'users'");
        $current_status['users_table'] = $result && $result->num_rows > 0 ? "✅ Exists" : "❌ Missing";
        
        // Check admin user
        if ($result && $result->num_rows > 0) {
            $admin_result = $test_conn->query("SELECT COUNT(*) as count FROM users WHERE username = 'admin'");
            if ($admin_result) {
                $admin_count = $admin_result->fetch_assoc()['count'];
                $current_status['admin_user'] = $admin_count > 0 ? "✅ Exists" : "❌ Missing";
            }
            
            // Count total users
            $count_result = $test_conn->query("SELECT COUNT(*) as total FROM users");
            if ($count_result) {
                $count = $count_result->fetch_assoc();
                $current_status['total_users'] = $count['total'] . " users";
            }
        }
        
        $test_conn->close();
    }
} catch (Exception $e) {
    $current_status['connection'] = "❌ Error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Setup - Shiftur</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 2rem;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: #8b5cf6;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .content {
            padding: 2rem;
        }
        
        .status-section {
            background: #f9fafb;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 1rem;
            background: #8b5cf6;
            color: white;
            text-decoration: none;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .btn:hover {
            background: #7c3aed;
        }
        
        .messages {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .message {
            margin-bottom: 0.5rem;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .back-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .back-links a {
            color: #8b5cf6;
            text-decoration: none;
            margin: 0 1rem;
        }
        
        .back-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Authentication Setup</h1>
            <p>Setup database tables and users for authentication</p>
        </div>
        
        <div class="content">
            <!-- Current Status -->
            <div class="status-section">
                <h3>Current Status</h3>
                <?php foreach ($current_status as $label => $status): ?>
                    <div class="status-item">
                        <span><?php echo ucwords(str_replace('_', ' ', $label)); ?></span>
                        <span><?php echo $status; ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Messages -->
            <?php if (!empty($messages)): ?>
                <div class="messages">
                    <h3>Setup Results</h3>
                    <?php foreach ($messages as $message): ?>
                        <div class="message"><?php echo htmlspecialchars($message); ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <!-- Setup Button -->
            <form method="POST">
                <button type="submit" name="setup_auth" class="btn">
                    Setup Authentication System
                </button>
            </form>
            
            <?php if ($success): ?>
                <a href="login.php" class="btn" style="background: #10b981;">Test Login</a>
                <a href="signup.php" class="btn" style="background: #f59e0b;">Test Signup</a>
            <?php endif; ?>
            
            <div class="back-links">
                <a href="test-auth.php">Test Authentication</a>
                <a href="admin/create-database.php">Full Database Setup</a>
                <a href="index.php">Back to Website</a>
            </div>
        </div>
    </div>
</body>
</html>
