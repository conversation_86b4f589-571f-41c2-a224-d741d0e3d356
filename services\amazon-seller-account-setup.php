<?php
// Page configuration
$page_title = "Amazon Seller Account Setup Guide | Professional Setup Service | Shiftur";
$page_description = "Expert Amazon seller account setup service for US businesses. Avoid verification delays and account deactivation with our professional setup guide and service.";

// Base path for navigation
$base_path = '../';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <!-- Hero Section -->
    <section class="hero service-hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text" data-aos="fade-up">
                    <h1 class="hero-title">
                        Amazon Seller Account Setup: Your Gateway to <span class="text-purple">E-commerce Success</span>
                    </h1>
                    <p class="hero-description" data-aos="fade-up" data-aos-delay="100">
                        Professional account setup service to avoid verification delays and get your Amazon business started right
                    </p>
                    <div class="hero-actions" data-aos="fade-up" data-aos-delay="200">
                        <a href="../contact.php" class="btn btn-primary">Get Professional Setup</a>
                        <a href="#setup-guide" class="btn btn-outline">Learn More</a>
                    </div>
                </div>
                <div class="hero-visual" data-aos="fade-left" data-aos-delay="300">
                    <img src="../assets/images/brand registry/ChatGPT Image Jul 18, 2025, 08_53_04 PM.png" alt="Amazon Seller Account Setup" class="hero-image">
                </div>
            </div>
        </div>
    </section>

    <!-- Introduction Section -->
    <section class="content-section" id="setup-guide">
        <div class="container">
            <div class="content-grid content-centered">
                <div class="content-text" data-aos="fade-up">
                    <h2 class="section-title">The Foundation of Your <span class="text-purple">Amazon Journey</span></h2>
                    <p class="content-description">
                        Starting your journey on Amazon begins with one crucial step: setting up your seller account. This foundational process is more than just filling out a form; it involves a detailed verification by Amazon to ensure a secure and trustworthy marketplace for everyone.
                    </p>
                    <p class="content-description">
                        While it might seem straightforward, the account setup and verification process can be complex, with specific document requirements that can cause significant delays if not met perfectly. This is where Shiftur steps in. We specialize in providing a seamless and error-free account setup experience for US-based startups and small businesses, ensuring you get it right the first time.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Verification Section -->
    <section class="content-section bg-secondary">
        <div class="container">
            <div class="content-grid content-centered">
                <div class="content-text" data-aos="fade-up">
                    <h2 class="section-title">Why Does Amazon Require <span class="text-purple">Verification?</span></h2>
                    <p class="content-description">
                        Amazon's verification process is designed to protect both buyers and sellers. By confirming the identity and legitimacy of every seller, Amazon maintains a trusted environment, preventing fraud and ensuring that only authentic businesses operate on its platform.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Document Requirements Section -->
    <section class="content-section">
        <div class="container">
            <div class="content-grid content-centered">
                <div class="content-text" data-aos="fade-up">
                    <h2 class="section-title">Core Documents Required for <span class="text-purple">US-Based Businesses</span></h2>
                    <p class="content-description">
                        For a US-based business, Amazon will typically require you to submit clear, valid, and unaltered copies of specific documents to verify three key areas: your identity, your business's legal standing, and your physical address.
                    </p>
                    <p class="content-description">
                        Here is a simplified overview of what you'll need:
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Document Requirements Table -->
    <section class="pricing-section">
        <div class="container">
            <div class="pricing-table-container" data-aos="fade-up">
                <div class="pricing-table">
                    <div class="pricing-header">
                        <div class="pricing-cell feature-cell">Document Type</div>
                        <div class="pricing-cell">What It Verifies</div>
                        <div class="pricing-cell">Common Examples for US Business</div>
                    </div>

                    <div class="pricing-row">
                        <div class="pricing-cell feature-cell">1. Identity Document</div>
                        <div class="pricing-cell">The identity of the business owner or the primary contact person.</div>
                        <div class="pricing-cell">
                            • A valid, unexpired Passport<br>
                            • A valid, unexpired Driver's License
                        </div>
                    </div>

                    <div class="pricing-row">
                        <div class="pricing-cell feature-cell">2. Business Verification Document</div>
                        <div class="pricing-cell">Proof that your business is legally registered and active in the United States.</div>
                        <div class="pricing-cell">
                            • EIN (Employer Identification Number) Confirmation Letter from the IRS<br>
                            • Articles of Organization (for an LLC)<br>
                            • Certificate of Good Standing from your state
                        </div>
                    </div>

                    <div class="pricing-row">
                        <div class="pricing-cell feature-cell">3. Proof of Address (PoA)</div>
                        <div class="pricing-cell">A document confirming your business or residential address. This address must match the one provided during registration.</div>
                        <div class="pricing-cell">
                            • A recent utility bill (Electricity, Gas, Water, or Internet)<br>
                            • A recent business bank account statement<br>
                            • A recent business credit card statement
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Key Requirements Section -->
    <section class="content-section bg-secondary">
        <div class="container">
            <div class="content-grid content-centered">
                <div class="content-text" data-aos="fade-up">
                    <h2 class="section-title">Key Requirements for <span class="text-purple">All Documents</span></h2>
                    <p class="content-description">
                        Submitting documents that don't meet Amazon's strict standards is the most common reason for delays. To avoid rejection, all your documents must be:
                    </p>
                    <div class="content-features">
                        <div class="feature-item">
                            <i class="fas fa-image"></i>
                            <div>
                                <h4>High-Resolution and Clear</h4>
                                <p>Scans or photos must be sharp and easy to read.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-palette"></i>
                            <div>
                                <h4>Full-Color</h4>
                                <p>Black and white copies are generally not accepted.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-expand"></i>
                            <div>
                                <h4>Complete and Uncropped</h4>
                                <p>All four corners of the document must be visible. If the document has a front and back, both sides are required.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-shield-alt"></i>
                            <div>
                                <h4>Authentic and Unaltered</h4>
                                <p>Do not edit or modify the documents in any way.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-calendar-check"></i>
                            <div>
                                <h4>Valid and Not Expired</h4>
                                <p>Ensure all documents are current.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-file"></i>
                            <div>
                                <h4>In the Correct Format</h4>
                                <p>Upload files as PDF, JPG, or PNG.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- High Cost of Mistakes Section -->
    <section class="content-section">
        <div class="container">
            <div class="content-grid content-centered">
                <div class="content-text" data-aos="fade-up">
                    <h2 class="section-title">The High Cost of a <span class="text-purple">Small Mistake</span></h2>
                    <p class="content-description">
                        The account verification process is unforgiving. A small mistake—like a blurry image, a slight name mismatch between documents, or an incorrect address—is not just a minor setback. If Amazon's system flags any inconsistencies, your new account can be immediately deactivated.
                    </p>
                    <p class="content-description">
                        This deactivation triggers a lengthy re-verification process where you must appeal and provide additional information to clear Amazon's doubts. This cycle can be incredibly time-consuming and frustrating for new sellers, often taking weeks or even months to resolve, putting your entire business launch on hold.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Shiftur Section -->
    <section class="content-section bg-secondary">
        <div class="container">
            <div class="content-grid content-centered">
                <div class="content-text" data-aos="fade-up">
                    <h2 class="section-title">Why Navigate This Alone? Let <span class="text-purple">Shiftur</span> Handle It.</h2>
                    <p class="content-description">
                        Instead of risking costly delays and the frustration of a deactivated account, let the experts at Shiftur manage your Amazon seller account setup from start to finish.
                    </p>
                    <div class="content-features">
                        <div class="feature-item">
                            <i class="fas fa-cogs"></i>
                            <div>
                                <h4>We Know the System</h4>
                                <p>We are experts in Amazon's US-specific requirements.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-shield-check"></i>
                            <div>
                                <h4>We Prevent Errors</h4>
                                <p>We review every document to ensure it meets Amazon's standards before submission.</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-clock"></i>
                            <div>
                                <h4>We Save You Time</h4>
                                <p>Our streamlined process gets your account verified and ready to sell as quickly as possible.</p>
                            </div>
                        </div>
                    </div>
                    <p class="content-description">
                        Focus on building your brand, and let us handle the complexities of getting you started on Amazon.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="content-section bg-purple text-white">
        <div class="container">
            <div class="content-grid content-centered">
                <div class="content-text text-center" data-aos="fade-up">
                    <h2 class="section-title text-white">Ready to Start Your Amazon Journey <span class="text-yellow">Without the Hassle?</span></h2>
                    <p class="content-description text-white opacity-90">
                        Contact us today for a seamless account setup and get your Amazon business started right the first time.
                    </p>
                    <div class="cta-contact" data-aos="fade-up" data-aos-delay="200">
                        <p class="cta-email">
                            <i class="fas fa-envelope"></i>
                            Contact us for seamless account setup:
                            <a href="mailto:<EMAIL>" class="email-link"><EMAIL></a>
                        </p>
                    </div>
                    <div class="content-actions" data-aos="fade-up" data-aos-delay="300">
                        <a href="../contact.php" class="btn btn-primary">
                            <i class="fas fa-rocket"></i>
                            Get Professional Setup
                        </a>
                        <a href="tel:+**********" class="btn btn-outline">
                            <i class="fas fa-phone"></i>
                            Call Now
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="../assets/js/main.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });
    </script>
</body>
</html>
