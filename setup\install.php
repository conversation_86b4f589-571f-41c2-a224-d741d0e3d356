<?php
// Database Setup Script for Shiftur Light Theme

// Database configuration
$db_host = 'localhost';
$db_username = 'root';
$db_password = '';
$db_name = 'shiftur_light';

$success = false;
$error = '';

if ($_POST && isset($_POST['install'])) {
    try {
        // Connect to MySQL without selecting database
        $conn = new mysqli($db_host, $db_username, $db_password);
        
        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }
        
        // Create database
        $sql = "CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        if (!$conn->query($sql)) {
            throw new Exception("Error creating database: " . $conn->error);
        }
        
        // Select the database
        $conn->select_db($db_name);
        
        // Read and execute SQL file
        $sql_file = __DIR__ . '/database.sql';
        if (!file_exists($sql_file)) {
            throw new Exception("SQL file not found: $sql_file");
        }
        
        $sql_content = file_get_contents($sql_file);
        
        // Remove the CREATE DATABASE line since we already created it
        $sql_content = preg_replace('/CREATE DATABASE.*?;/i', '', $sql_content);
        $sql_content = preg_replace('/USE.*?;/i', '', $sql_content);
        
        // Split SQL into individual queries
        $queries = array_filter(array_map('trim', explode(';', $sql_content)));
        
        foreach ($queries as $query) {
            if (!empty($query)) {
                if (!$conn->query($query)) {
                    throw new Exception("Error executing query: " . $conn->error . "\nQuery: " . substr($query, 0, 100) . "...");
                }
            }
        }
        
        $conn->close();
        $success = true;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shiftur Installation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .install-container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo h1 {
            color: #8b5cf6;
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
        }
        
        .logo p {
            color: #64748b;
            font-size: 1.1rem;
        }
        
        .install-form {
            margin-top: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
        }
        
        input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s;
        }
        
        input:focus {
            outline: none;
            border-color: #8b5cf6;
        }
        
        .btn {
            width: 100%;
            padding: 1rem 2rem;
            background: #8b5cf6;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background: #7c3aed;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .alert-success {
            background: #f0fdf4;
            color: #16a34a;
            border: 1px solid #bbf7d0;
        }
        
        .alert-error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .info-box {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .info-box h3 {
            color: #374151;
            margin-bottom: 1rem;
        }
        
        .info-box ul {
            color: #64748b;
            padding-left: 1.5rem;
        }
        
        .info-box li {
            margin-bottom: 0.5rem;
        }
        
        .success-actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .btn-outline {
            background: transparent;
            color: #8b5cf6;
            border: 2px solid #8b5cf6;
        }
        
        .btn-outline:hover {
            background: #8b5cf6;
            color: white;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="logo">
            <h1>Shiftur</h1>
            <p>Database Installation</p>
        </div>
        
        <?php if ($success): ?>
            <div class="alert alert-success">
                <i>✓</i>
                <span>Database installed successfully!</span>
            </div>
            
            <div class="info-box">
                <h3>Installation Complete</h3>
                <p>Your Shiftur website is now ready to use. Here are your next steps:</p>
                <ul>
                    <li><strong>Admin Login:</strong> Username: admin, Password: password</li>
                    <li><strong>Change Password:</strong> Please change the default password immediately</li>
                    <li><strong>Add Content:</strong> Use the admin panel to add your content</li>
                    <li><strong>Security:</strong> Delete this installation file for security</li>
                </ul>
            </div>
            
            <div class="success-actions">
                <a href="../index.php" class="btn" style="text-decoration: none; text-align: center;">View Website</a>
                <a href="../admin/login.php" class="btn btn-outline" style="text-decoration: none; text-align: center;">Admin Login</a>
            </div>
            
        <?php else: ?>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i>✗</i>
                    <span><?php echo htmlspecialchars($error); ?></span>
                </div>
            <?php endif; ?>
            
            <div class="info-box">
                <h3>Database Setup</h3>
                <p>This will create the database and tables needed for your Shiftur website.</p>
                <ul>
                    <li>Database: shiftur_light</li>
                    <li>Tables: users, services, portfolio, testimonials, etc.</li>
                    <li>Sample data will be included</li>
                    <li>Default admin user will be created</li>
                </ul>
            </div>
            
            <form method="POST" class="install-form">
                <div class="form-group">
                    <label>Database Host</label>
                    <input type="text" name="db_host" value="localhost" readonly>
                </div>
                
                <div class="form-group">
                    <label>Database Username</label>
                    <input type="text" name="db_username" value="root" readonly>
                </div>
                
                <div class="form-group">
                    <label>Database Password</label>
                    <input type="password" name="db_password" placeholder="Usually empty for XAMPP">
                </div>
                
                <div class="form-group">
                    <label>Database Name</label>
                    <input type="text" name="db_name" value="shiftur_light" readonly>
                </div>
                
                <button type="submit" name="install" class="btn">
                    Install Database
                </button>
            </form>
            
        <?php endif; ?>
    </div>
</body>
</html>
