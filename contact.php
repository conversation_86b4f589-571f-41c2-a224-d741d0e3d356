<?php
// Session management
session_start();
require_once 'includes/website-auth.php';

// Page configuration
$current_page = 'contact';
$page_title = 'Contact Us - Shiftur | Get In Touch';
$page_description = 'Contact Shiftur for digital solutions. Get in touch with our expert team for web development, app development, digital marketing, and AI business solutions.';

// Database connection is already available through website-auth.php

$success = '';
$error = '';

// Handle form submission
if ($_POST && isset($_POST['submit_contact'])) {
    $name = sanitizeInput($_POST['name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $company = sanitizeInput($_POST['company'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $message = sanitizeInput($_POST['message'] ?? '');
    $service_interest = sanitizeInput($_POST['service_interest'] ?? '');
    $business_type = sanitizeInput($_POST['business_type'] ?? '');
    
    // Validation
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        $error = 'Please fill in all required fields.';
    } elseif (!validateEmail($email)) {
        $error = 'Please enter a valid email address.';
    } else {
        // Save to database
        if ($conn) {
            $data = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'company' => $company,
                'subject' => $subject,
                'message' => $message,
                'service_interest' => $service_interest,
                'business_type' => $business_type,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'status' => 'new'
            ];
            
            $result = insertRecord($conn, 'contact_messages', $data);
            if ($result) {
                $success = 'Thank you for your message! We\'ll get back to you within 24 hours.';
                // Clear form data
                $_POST = [];
            } else {
                $error = 'Sorry, there was an error sending your message. Please try again.';
            }
        } else {
            $error = 'Sorry, there was an error sending your message. Please try again.';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation -->
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <?php include 'includes/header.php'; ?>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <div class="page-header-content" data-aos="fade-up">
                <h1 class="page-title">Contact <span class="text-purple">Us</span></h1>
                <p class="page-description">
                    Ready to transform your business? Get in touch with our expert team today
                </p>
                <nav class="breadcrumb">
                    <a href="index.php">Home</a>
                    <span>/</span>
                    <span>Contact</span>
                </nav>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section">
        <div class="container">
            <div class="contact-grid">
                <!-- Contact Information -->
                <div class="contact-info" data-aos="fade-right">
                    <h2 class="section-title">Get In <span class="text-orange">Touch</span></h2>
                    <p class="contact-description">
                        We'd love to hear from you. Send us a message and we'll respond as soon as possible.
                    </p>
                    
                    <div class="contact-cards">
                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="contact-details">
                                <h3>Our Office</h3>
                                <p>2540 Old Court Road<br>Pikesville, Baltimore<br>Maryland, USA</p>
                            </div>
                        </div>

                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="contact-details">
                                <h3>Phone Number</h3>
                                <p><a href="tel:+14432640096">+****************</a></p>
                            </div>
                        </div>

                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="contact-details">
                                <h3>Email Address</h3>
                                <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                            </div>
                        </div>

                        <div class="contact-card">
                            <div class="contact-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="contact-details">
                                <h3>Business Hours</h3>
                                <p>Monday - Friday: 9:00 AM - 6:00 PM<br>Saturday: 10:00 AM - 4:00 PM</p>
                            </div>
                        </div>
                    </div>

                    <div class="social-links">
                        <h3>Follow Us</h3>
                        <div class="social-icons">
                            <a href="#" class="social-link">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="#" class="social-link">
                                <i class="fab fa-instagram"></i>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Contact Form -->
                <div class="contact-form-container" data-aos="fade-left">
                    <div class="form-card">
                        <h3>Send Us a Message</h3>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-error">
                                <i class="fas fa-exclamation-circle"></i>
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="contact-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="name">Full Name *</label>
                                    <input type="text" id="name" name="name" required 
                                           value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>"
                                           placeholder="Enter your full name">
                                </div>
                                <div class="form-group">
                                    <label for="email">Email Address *</label>
                                    <input type="email" id="email" name="email" required 
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                           placeholder="Enter your email">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                                           placeholder="Enter your phone number">
                                </div>
                                <div class="form-group">
                                    <label for="company">Company</label>
                                    <input type="text" id="company" name="company" 
                                           value="<?php echo htmlspecialchars($_POST['company'] ?? ''); ?>"
                                           placeholder="Enter your company name">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="service_interest">Service Interest</label>
                                    <select id="service_interest" name="service_interest">
                                        <option value="">Select a service</option>
                                        <option value="web-app-development" <?php echo ($_POST['service_interest'] ?? '') === 'web-app-development' ? 'selected' : ''; ?>>Web & App Development</option>
                                        <option value="amazon-services" <?php echo ($_POST['service_interest'] ?? '') === 'amazon-services' ? 'selected' : ''; ?>>Amazon Services</option>
                                        <option value="digital-marketing" <?php echo ($_POST['service_interest'] ?? '') === 'digital-marketing' ? 'selected' : ''; ?>>Digital Marketing</option>
                                        <option value="ai-business-solutions" <?php echo ($_POST['service_interest'] ?? '') === 'ai-business-solutions' ? 'selected' : ''; ?>>AI Business Solutions</option>
                                        <option value="graphic-design" <?php echo ($_POST['service_interest'] ?? '') === 'graphic-design' ? 'selected' : ''; ?>>Graphic Design</option>
                                        <option value="consultation" <?php echo ($_POST['service_interest'] ?? '') === 'consultation' ? 'selected' : ''; ?>>Consultation</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="business_type">Business Type</label>
                                    <select id="business_type" name="business_type">
                                        <option value="">Select business type</option>
                                        <option value="Startup" <?php echo ($_POST['business_type'] ?? '') === 'Startup' ? 'selected' : ''; ?>>Startup</option>
                                        <option value="Small Business" <?php echo ($_POST['business_type'] ?? '') === 'Small Business' ? 'selected' : ''; ?>>Small Business</option>
                                        <option value="Enterprise" <?php echo ($_POST['business_type'] ?? '') === 'Enterprise' ? 'selected' : ''; ?>>Enterprise</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="subject">Subject *</label>
                                <input type="text" id="subject" name="subject" required 
                                       value="<?php echo htmlspecialchars($_POST['subject'] ?? ''); ?>"
                                       placeholder="Enter the subject">
                            </div>
                            
                            <div class="form-group">
                                <label for="message">Message *</label>
                                <textarea id="message" name="message" rows="6" required 
                                          placeholder="Tell us about your project or requirements"><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                            </div>
                            
                            <button type="submit" name="submit_contact" class="btn btn-primary btn-full">
                                <span>Send Message</span>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="map-section">
        <div class="container">
            <div class="map-container" data-aos="fade-up">
                <div class="map-placeholder">
                    <i class="fas fa-map-marker-alt"></i>
                    <h3>Our Location</h3>
                    <p>2540 Old Court Road, Pikesville, Baltimore, Maryland, USA</p>
                    <a href="https://maps.google.com/?q=2540+Old+Court+Road+Pikesville+Baltimore+Maryland+USA" 
                       target="_blank" class="btn btn-outline">
                        <span>View on Google Maps</span>
                        <i class="fas fa-external-link-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <?php include 'includes/footer.php'; ?>

    <!-- Scripts -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
