<?php
// Simple admin login test
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Admin Login Test</h1>";

// Start session
session_start();

// Include required files
try {
    require_once '../includes/db.php';
    echo "<p style='color: green;'>✅ Database connection loaded</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
    exit;
}

try {
    require_once '../includes/admin-auth.php';
    echo "<p style='color: green;'>✅ Admin auth loaded</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Admin auth error: " . $e->getMessage() . "</p>";
    exit;
}

// Check database connection
if (!$conn) {
    echo "<p style='color: red;'>❌ No database connection</p>";
    exit;
}

echo "<p style='color: green;'>✅ Database connected</p>";

// Initialize admin system
try {
    initializeAdminSystem();
    echo "<p style='color: green;'>✅ Admin system initialized</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Admin system error: " . $e->getMessage() . "</p>";
}

$message = '';

// Handle login
if ($_POST && isset($_POST['login'])) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    echo "<h2>Login Attempt</h2>";
    echo "<p>Username: " . htmlspecialchars($username) . "</p>";
    
    if (empty($username) || empty($password)) {
        $message = '<p style="color: red;">Please enter both username and password</p>';
    } else {
        try {
            // Test authentication
            $user = authenticateAdminUser($username, $password);
            
            if ($user) {
                echo '<p style="color: green;">✅ Authentication successful!</p>';
                echo '<p>User found: ' . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . '</p>';
                echo '<p>Role: ' . htmlspecialchars($user['role']) . '</p>';
                echo '<p>Status: ' . htmlspecialchars($user['status']) . '</p>';
                
                // Try to login
                try {
                    loginAdminUser($user);
                    echo '<p style="color: green;">✅ Login successful!</p>';
                    echo '<p>Session admin_user_id: ' . ($_SESSION['admin_user_id'] ?? 'Not set') . '</p>';
                    echo '<p><a href="dashboard.php">Go to Dashboard</a></p>';
                } catch (Exception $e) {
                    echo '<p style="color: red;">❌ Login function error: ' . $e->getMessage() . '</p>';
                }
                
            } else {
                echo '<p style="color: red;">❌ Authentication failed</p>';
                echo '<p>Invalid username/email or password</p>';
                
                // Check if user exists
                $check_user = fetchOne($conn, "SELECT * FROM admin_users WHERE username = ? OR email = ?", [$username, $username]);
                if ($check_user) {
                    echo '<p style="color: orange;">User exists but password is wrong</p>';
                    echo '<p>User status: ' . htmlspecialchars($check_user['status']) . '</p>';
                } else {
                    echo '<p style="color: orange;">User not found in database</p>';
                }
            }
            
        } catch (Exception $e) {
            $message = '<p style="color: red;">Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
        }
    }
}

// Check current session
echo "<h2>Session Status</h2>";
if (isAdminUserLoggedIn()) {
    echo '<p style="color: green;">✅ Admin user is logged in</p>';
    echo '<p>User ID: ' . ($_SESSION['admin_user_id'] ?? 'Not set') . '</p>';
    echo '<p>Username: ' . ($_SESSION['admin_username'] ?? 'Not set') . '</p>';
    echo '<p>Name: ' . ($_SESSION['admin_user_name'] ?? 'Not set') . '</p>';
    echo '<p><a href="dashboard.php">Go to Dashboard</a></p>';
    echo '<p><a href="logout.php">Logout</a></p>';
} else {
    echo '<p style="color: orange;">No admin user logged in</p>';
}

// Show admin users
echo "<h2>Available Admin Users</h2>";
try {
    $admin_users = fetchAll($conn, "SELECT id, username, email, first_name, last_name, role, status FROM admin_users");
    if (!empty($admin_users)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Role</th><th>Status</th></tr>";
        foreach ($admin_users as $user) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($user['id']) . "</td>";
            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
            echo "<td>" . htmlspecialchars($user['email']) . "</td>";
            echo "<td>" . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . "</td>";
            echo "<td>" . htmlspecialchars($user['role']) . "</td>";
            echo "<td>" . htmlspecialchars($user['status']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No admin users found</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error getting admin users: " . $e->getMessage() . "</p>";
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Admin Login Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .form-group { margin: 10px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { padding: 10px; width: 300px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .btn:hover { background: #005a87; }
        .login-form { background: #f9f9f9; padding: 20px; border-radius: 8px; max-width: 400px; margin: 20px 0; }
    </style>
</head>
<body>
    <?php echo $message; ?>
    
    <div class="login-form">
        <h2>Test Admin Login</h2>
        <form method="POST">
            <div class="form-group">
                <label>Username/Email:</label>
                <input type="text" name="username" value="<EMAIL>" required>
            </div>
            <div class="form-group">
                <label>Password:</label>
                <input type="password" name="password" value="admin123" required>
            </div>
            <button type="submit" name="login" class="btn">Test Login</button>
        </form>
    </div>
    
    <h2>Default Credentials</h2>
    <p><strong>Email:</strong> <EMAIL></p>
    <p><strong>Password:</strong> admin123</p>
    
    <h2>Links</h2>
    <p>
        <a href="login.php">Regular Admin Login</a> | 
        <a href="dashboard.php">Dashboard</a> | 
        <a href="../debug-admin-login.php">Full Debug</a>
    </p>
    
    <p style="color: red;"><strong>⚠️ Delete this test file after debugging!</strong></p>
</body>
</html>
