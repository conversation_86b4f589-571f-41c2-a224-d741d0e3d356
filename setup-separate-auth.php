<?php
// Setup separate authentication systems for admin and website users
$db_host = 'localhost';
$db_username = 'root';
$db_password = '';
$db_name = 'shiftur_light';

$messages = [];
$success = false;

if ($_POST && isset($_POST['setup_auth'])) {
    try {
        // Connect to MySQL server (without database)
        $conn = new mysqli($db_host, $db_username, $db_password);
        
        if ($conn->connect_error) {
            throw new Exception("Connection failed: " . $conn->connect_error);
        }
        
        $messages[] = "✅ Connected to MySQL server";
        
        // Create database if it doesn't exist
        $sql = "CREATE DATABASE IF NOT EXISTS `$db_name` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
        if ($conn->query($sql) === TRUE) {
            $messages[] = "✅ Database '$db_name' created or already exists";
        } else {
            throw new Exception("Error creating database: " . $conn->error);
        }
        
        // Select the database
        $conn->select_db($db_name);
        $messages[] = "✅ Selected database '$db_name'";
        
        // Create admin_users table (for admin panel)
        $admin_users_table = "
        CREATE TABLE IF NOT EXISTS admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            role ENUM('admin', 'editor') DEFAULT 'editor',
            status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
            last_login DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($admin_users_table) === TRUE) {
            $messages[] = "✅ Admin users table created or already exists";
        } else {
            throw new Exception("Error creating admin_users table: " . $conn->error);
        }
        
        // Create website_users table (for website login/signup)
        $website_users_table = "
        CREATE TABLE IF NOT EXISTS website_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            phone VARCHAR(20) NULL,
            company VARCHAR(100) NULL,
            status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
            email_verified BOOLEAN DEFAULT FALSE,
            verification_token VARCHAR(255) NULL,
            reset_token VARCHAR(255) NULL,
            reset_token_expires DATETIME NULL,
            last_login DATETIME NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($website_users_table) === TRUE) {
            $messages[] = "✅ Website users table created or already exists";
        } else {
            throw new Exception("Error creating website_users table: " . $conn->error);
        }
        
        // Create admin_activity_logs table
        $admin_activity_table = "
        CREATE TABLE IF NOT EXISTS admin_activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            admin_user_id INT NULL,
            action VARCHAR(255) NOT NULL,
            details TEXT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($admin_activity_table) === TRUE) {
            $messages[] = "✅ Admin activity logs table created or already exists";
        } else {
            throw new Exception("Error creating admin_activity_logs table: " . $conn->error);
        }
        
        // Create website_activity_logs table
        $website_activity_table = "
        CREATE TABLE IF NOT EXISTS website_activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            website_user_id INT NULL,
            action VARCHAR(255) NOT NULL,
            details TEXT NULL,
            ip_address VARCHAR(45) NULL,
            user_agent TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (website_user_id) REFERENCES website_users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if ($conn->query($website_activity_table) === TRUE) {
            $messages[] = "✅ Website activity logs table created or already exists";
        } else {
            throw new Exception("Error creating website_activity_logs table: " . $conn->error);
        }
        
        // Check if admin user exists
        $admin_check = $conn->query("SELECT COUNT(*) as count FROM admin_users WHERE username = 'admin'");
        if ($admin_check) {
            $admin_count = $admin_check->fetch_assoc()['count'];
            if ($admin_count > 0) {
                $messages[] = "✅ Admin user already exists";
            } else {
                // Create admin user
                $password_hash = password_hash('password', PASSWORD_DEFAULT);
                $admin_sql = "INSERT INTO admin_users (username, email, password, first_name, last_name, role, status) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($admin_sql);
                $stmt->bind_param('sssssss', 
                    $username, $email, $password_hash, $first_name, $last_name, $role, $status
                );
                
                $username = 'admin';
                $email = '<EMAIL>';
                $first_name = 'Admin';
                $last_name = 'User';
                $role = 'admin';
                $status = 'active';
                
                if ($stmt->execute()) {
                    $messages[] = "✅ Admin user created successfully";
                    $messages[] = "🔐 Admin Login: username=admin, password=password";
                } else {
                    $messages[] = "❌ Failed to create admin user: " . $stmt->error;
                }
                $stmt->close();
            }
        }
        
        // Create a test website user
        $test_check = $conn->query("SELECT COUNT(*) as count FROM website_users WHERE username = 'testuser'");
        if ($test_check) {
            $test_count = $test_check->fetch_assoc()['count'];
            if ($test_count == 0) {
                $password_hash = password_hash('testpass', PASSWORD_DEFAULT);
                $test_sql = "INSERT INTO website_users (username, email, password, first_name, last_name, status) VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $conn->prepare($test_sql);
                $stmt->bind_param('ssssss', 
                    $username, $email, $password_hash, $first_name, $last_name, $status
                );
                
                $username = 'testuser';
                $email = '<EMAIL>';
                $first_name = 'Test';
                $last_name = 'User';
                $status = 'active';
                
                if ($stmt->execute()) {
                    $messages[] = "✅ Test website user created successfully";
                    $messages[] = "🔐 Website Login: username=testuser, password=testpass";
                } else {
                    $messages[] = "❌ Failed to create test website user: " . $stmt->error;
                }
                $stmt->close();
            }
        }
        
        // Drop old users table if it exists (to avoid confusion)
        $conn->query("DROP TABLE IF EXISTS users");
        $conn->query("DROP TABLE IF EXISTS activity_logs");
        $messages[] = "✅ Cleaned up old tables";
        
        $success = true;
        $conn->close();
        
    } catch (Exception $e) {
        $messages[] = "❌ Error: " . $e->getMessage();
    }
}

// Test current connection
$current_status = [];
try {
    $test_conn = new mysqli($db_host, $db_username, $db_password, $db_name);
    if ($test_conn->connect_error) {
        $current_status['connection'] = "❌ Failed: " . $test_conn->connect_error;
    } else {
        $current_status['connection'] = "✅ Connected";
        
        // Check admin_users table
        $result = $test_conn->query("SHOW TABLES LIKE 'admin_users'");
        $current_status['admin_users_table'] = $result && $result->num_rows > 0 ? "✅ Exists" : "❌ Missing";
        
        // Check website_users table
        $result = $test_conn->query("SHOW TABLES LIKE 'website_users'");
        $current_status['website_users_table'] = $result && $result->num_rows > 0 ? "✅ Exists" : "❌ Missing";
        
        // Count admin users
        if ($current_status['admin_users_table'] === "✅ Exists") {
            $admin_count_result = $test_conn->query("SELECT COUNT(*) as total FROM admin_users");
            if ($admin_count_result) {
                $admin_count = $admin_count_result->fetch_assoc();
                $current_status['admin_users_count'] = $admin_count['total'] . " admin users";
            }
        }
        
        // Count website users
        if ($current_status['website_users_table'] === "✅ Exists") {
            $website_count_result = $test_conn->query("SELECT COUNT(*) as total FROM website_users");
            if ($website_count_result) {
                $website_count = $website_count_result->fetch_assoc();
                $current_status['website_users_count'] = $website_count['total'] . " website users";
            }
        }
        
        $test_conn->close();
    }
} catch (Exception $e) {
    $current_status['connection'] = "❌ Error: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Separate Authentication Setup - Shiftur</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 2rem;
        }
        
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .header {
            background: #8b5cf6;
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .content {
            padding: 2rem;
        }
        
        .status-section {
            background: #f9fafb;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 1rem;
            background: #8b5cf6;
            color: white;
            text-decoration: none;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .btn:hover {
            background: #7c3aed;
        }
        
        .btn.success {
            background: #10b981;
        }
        
        .btn.warning {
            background: #f59e0b;
        }
        
        .messages {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .message {
            margin-bottom: 0.5rem;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .info-box {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }
        
        .back-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .back-links a {
            color: #8b5cf6;
            text-decoration: none;
            margin: 0 1rem;
        }
        
        .back-links a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Separate Authentication Setup</h1>
            <p>Create separate tables for admin and website users</p>
        </div>
        
        <div class="content">
            <div class="info-box">
                <h3>🔄 System Separation</h3>
                <p><strong>Admin Users:</strong> For admin panel access (admin_users table)</p>
                <p><strong>Website Users:</strong> For website login/signup (website_users table)</p>
                <p>This ensures complete separation between admin and customer accounts.</p>
            </div>
            
            <!-- Current Status -->
            <div class="status-section">
                <h3>Current Status</h3>
                <?php foreach ($current_status as $label => $status): ?>
                    <div class="status-item">
                        <span><?php echo ucwords(str_replace('_', ' ', $label)); ?></span>
                        <span><?php echo $status; ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <!-- Messages -->
            <?php if (!empty($messages)): ?>
                <div class="messages">
                    <h3>Setup Results</h3>
                    <?php foreach ($messages as $message): ?>
                        <div class="message"><?php echo htmlspecialchars($message); ?></div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <!-- Setup Button -->
            <form method="POST">
                <button type="submit" name="setup_auth" class="btn">
                    Setup Separate Authentication Tables
                </button>
            </form>
            
            <?php if ($success): ?>
                <a href="admin/login.php" class="btn success">Test Admin Login</a>
                <a href="login.php" class="btn warning">Test Website Login</a>
                <a href="signup.php" class="btn warning">Test Website Signup</a>
            <?php endif; ?>
            
            <div class="back-links">
                <a href="debug-separate-auth.php">Debug Authentication</a>
                <a href="index.php">Back to Website</a>
            </div>
        </div>
    </div>
</body>
</html>
