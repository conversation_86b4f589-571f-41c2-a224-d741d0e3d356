<?php
// Simple database test page
require_once 'includes/db.php';

echo "<h1>Database Connection Test</h1>";

if ($conn) {
    echo "<p style='color: green;'>✓ Database connection successful!</p>";
    
    // Test if users table exists and has data
    try {
        $result = $conn->query("SELECT COUNT(*) as count FROM users");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<p style='color: green;'>✓ Users table exists with " . $row['count'] . " users</p>";
            
            // Get admin user details
            $admin = $conn->query("SELECT username, email, role FROM users WHERE role = 'admin' LIMIT 1");
            if ($admin && $admin->num_rows > 0) {
                $adminData = $admin->fetch_assoc();
                echo "<p style='color: blue;'>Admin user found:</p>";
                echo "<ul>";
                echo "<li>Username: " . htmlspecialchars($adminData['username']) . "</li>";
                echo "<li>Email: " . htmlspecialchars($adminData['email']) . "</li>";
                echo "<li>Role: " . htmlspecialchars($adminData['role']) . "</li>";
                echo "</ul>";
                echo "<p><strong>Default login credentials:</strong></p>";
                echo "<ul>";
                echo "<li>Username: admin</li>";
                echo "<li>Password: password</li>";
                echo "</ul>";
            }
        }
        
        // Test services table
        $services = $conn->query("SELECT COUNT(*) as count FROM services");
        if ($services) {
            $row = $services->fetch_assoc();
            echo "<p style='color: green;'>✓ Services table exists with " . $row['count'] . " services</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Database error: " . $e->getMessage() . "</p>";
        echo "<p>Please run the database setup: <a href='setup/install.php'>Install Database</a></p>";
    }
    
} else {
    echo "<p style='color: red;'>✗ Database connection failed!</p>";
    echo "<p>Please check your database configuration or run setup: <a href='setup/install.php'>Install Database</a></p>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Website</a> | <a href='admin/login.php'>Admin Login →</a></p>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
}

h1 {
    color: #333;
    border-bottom: 2px solid #8b5cf6;
    padding-bottom: 10px;
}

p {
    margin: 10px 0;
    padding: 10px;
    background: white;
    border-radius: 5px;
    border-left: 4px solid #ddd;
}

ul {
    background: white;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}

a {
    color: #8b5cf6;
    text-decoration: none;
    font-weight: bold;
}

a:hover {
    text-decoration: underline;
}
</style>
