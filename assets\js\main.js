// ===== SHIFTUR LIGHT THEME - MAIN JAVASCRIPT =====

document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS
    if (typeof AOS !== 'undefined') {
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });
    }



    // Mobile menu is now handled in header.php

    // Mobile Dropdown Toggle
    const mobileDropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');
    mobileDropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
            const dropdown = this.parentElement;
            dropdown.classList.toggle('active');
            
            const icon = this.querySelector('i');
            if (dropdown.classList.contains('active')) {
                icon.style.transform = 'rotate(180deg)';
            } else {
                icon.style.transform = 'rotate(0deg)';
            }
        });
    });

    // Smooth Scrolling for Anchor Links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;
            
            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = target.offsetTop - headerHeight - 20;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
                
                // Close mobile menu if open
                if (mobileMenu && mobileMenu.classList.contains('active')) {
                    mobileMenu.classList.remove('active');
                    mobileMenuToggle.classList.remove('active');
                    document.body.style.overflow = '';
                }
            }
        });
    });

    // Header Scroll Effect
    const header = document.querySelector('.header');
    if (header) {
        let lastScrollTop = 0;
        
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
            
            // Hide/show header on scroll
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }
            
            lastScrollTop = scrollTop;
        });
    }

    // Form Validation and Submission
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Basic form validation
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('error');
                    
                    // Remove error class after user starts typing
                    field.addEventListener('input', function() {
                        this.classList.remove('error');
                    });
                } else {
                    field.classList.remove('error');
                }
            });
            
            // Email validation
            const emailFields = form.querySelectorAll('input[type="email"]');
            emailFields.forEach(field => {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (field.value && !emailRegex.test(field.value)) {
                    isValid = false;
                    field.classList.add('error');
                }
            });
            
            if (isValid) {
                // Show loading state
                const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
                if (submitBtn) {
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
                    submitBtn.disabled = true;
                    
                    // Simulate form submission (replace with actual AJAX call)
                    setTimeout(() => {
                        submitBtn.innerHTML = '<i class="fas fa-check"></i> Sent!';
                        setTimeout(() => {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                            form.reset();
                        }, 2000);
                    }, 1500);
                }
            }
        });
    });

    // Lazy Loading for Images
    const images = document.querySelectorAll('img[data-src]');
    if (images.length > 0) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }

    // Counter Animation - DISABLED (numbers display statically)
    // const counters = document.querySelectorAll('.stat-number, .counter');
    // Animation removed - numbers now display normally without running effect

    // Testimonial Slider (if exists)
    const testimonialSlider = document.querySelector('.testimonial-slider');
    if (testimonialSlider) {
        let currentSlide = 0;
        const slides = testimonialSlider.querySelectorAll('.testimonial-slide');
        const totalSlides = slides.length;
        
        if (totalSlides > 1) {
            // Auto-advance slides
            setInterval(() => {
                slides[currentSlide].classList.remove('active');
                currentSlide = (currentSlide + 1) % totalSlides;
                slides[currentSlide].classList.add('active');
            }, 5000);
        }
    }

    // Portfolio Filter (if exists)
    const filterButtons = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');
    
    if (filterButtons.length > 0 && portfolioItems.length > 0) {
        filterButtons.forEach(btn => {
            btn.addEventListener('click', function() {
                const filter = this.dataset.filter;
                
                // Update active button
                filterButtons.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                // Filter items
                portfolioItems.forEach(item => {
                    if (filter === 'all' || item.dataset.category === filter) {
                        item.style.display = 'block';
                        item.style.animation = 'fadeInUp 0.5s ease-in-out';
                    } else {
                        item.style.display = 'none';
                    }
                });
            });
        });
    }

    // Back to Top Button
    const backToTopBtn = document.createElement('button');
    backToTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    backToTopBtn.className = 'back-to-top';
    backToTopBtn.setAttribute('aria-label', 'Back to top');
    document.body.appendChild(backToTopBtn);
    
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });
    
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // Preloader (if exists)
    const preloader = document.querySelector('.preloader');
    if (preloader) {
        window.addEventListener('load', function() {
            preloader.style.opacity = '0';
            setTimeout(() => {
                preloader.style.display = 'none';
            }, 500);
        });
    }

    // Mobile menu initialization complete
});

// Utility Functions
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : 'exclamation'}-circle"></i>
        <span>${message}</span>
        <button class="notification-close"><i class="fas fa-times"></i></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.remove();
    }, 5000);
    
    // Manual close
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });
}

// ===== TEAM SLIDER =====
let currentSlideIndex = 0;
let currentMemberIndex = 0;
const totalSlides = 3; // Total number of slides (3+3+1 members)
const totalMembers = 7; // Total individual members
const isMobile = () => window.innerWidth <= 768;

function moveSlide(direction) {
    console.log('moveSlide called with direction:', direction);

    if (isMobile()) {
        // Mobile: Navigate through individual members
        moveMobileMember(direction);
    } else {
        // Desktop: Navigate through slides
        moveDesktopSlide(direction);
    }
}

function moveDesktopSlide(direction) {
    const slider = document.querySelector('.team-slider');
    const dots = document.querySelectorAll('.dot');

    if (!slider || !dots.length) return;

    currentSlideIndex += direction;

    // Loop around
    if (currentSlideIndex >= totalSlides) {
        currentSlideIndex = 0;
    } else if (currentSlideIndex < 0) {
        currentSlideIndex = totalSlides - 1;
    }

    console.log('Moving to desktop slide:', currentSlideIndex);

    // Move slider
    slider.style.transform = `translateX(-${currentSlideIndex * 100}%)`;

    // Update dots
    dots.forEach((dot, index) => {
        dot.classList.toggle('active', index === currentSlideIndex);
    });
}

function moveMobileMember(direction) {
    currentMemberIndex += direction;

    // Loop around
    if (currentMemberIndex >= totalMembers) {
        currentMemberIndex = 0;
    } else if (currentMemberIndex < 0) {
        currentMemberIndex = totalMembers - 1;
    }

    console.log('Moving to mobile member:', currentMemberIndex);
    showMobileMember(currentMemberIndex);
}

function showMobileMember(memberIndex) {
    // Hide all members
    const allMembers = document.querySelectorAll('.team-member');
    allMembers.forEach(member => {
        member.classList.remove('mobile-active');
    });

    // Show the specific member
    if (allMembers[memberIndex]) {
        allMembers[memberIndex].classList.add('mobile-active');
    }

    // Update dots for mobile (show which "group" the member belongs to)
    const dots = document.querySelectorAll('.dot');
    let slideGroup = 0;
    if (memberIndex <= 2) slideGroup = 0;
    else if (memberIndex <= 5) slideGroup = 1;
    else slideGroup = 2;

    dots.forEach((dot, index) => {
        dot.classList.toggle('active', index === slideGroup);
    });
}

function goToSlide(slideIndex) {
    console.log('goToSlide called with index:', slideIndex);

    if (isMobile()) {
        // Mobile: Go to first member of the selected slide group
        let memberIndex = 0;
        if (slideIndex === 1) memberIndex = 0; // Zohaib
        else if (slideIndex === 2) memberIndex = 3; // Faz
        else if (slideIndex === 3) memberIndex = 6; // Ahmer

        currentMemberIndex = memberIndex;
        showMobileMember(memberIndex);
    } else {
        // Desktop: Normal slide navigation
        const slider = document.querySelector('.team-slider');
        const dots = document.querySelectorAll('.dot');

        if (!slider || !dots.length) return;

        currentSlideIndex = slideIndex - 1; // Convert to 0-based index

        console.log('Going to desktop slide:', currentSlideIndex);

        // Move slider
        slider.style.transform = `translateX(-${currentSlideIndex * 100}%)`;

        // Update dots
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === currentSlideIndex);
        });
    }
}

// Initialize team slider when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeTeamSlider();
});

function initializeTeamSlider() {
    console.log('Initializing team slider...');
    console.log('Is mobile:', isMobile());

    // Initialize mobile state
    if (isMobile()) {
        showMobileMember(0); // Show first member (Zohaib)
    }

    // Add event listeners to navigation buttons
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    if (prevBtn) {
        // Click event
        prevBtn.addEventListener('click', function() {
            console.log('Previous button clicked');
            moveSlide(-1);
        });

        // Hover event - show previous slide
        prevBtn.addEventListener('mouseenter', function() {
            console.log('Previous button hovered');
            moveSlide(-1);
        });
    } else {
        console.log('Previous button not found');
    }

    if (nextBtn) {
        // Click event
        nextBtn.addEventListener('click', function() {
            console.log('Next button clicked');
            moveSlide(1);
        });

        // Hover event - show next slide
        nextBtn.addEventListener('mouseenter', function() {
            console.log('Next button hovered');
            moveSlide(1);
        });
    } else {
        console.log('Next button not found');
    }

    // Add event listeners to dots
    const dots = document.querySelectorAll('.dot');
    dots.forEach((dot, index) => {
        dot.addEventListener('click', function() {
            console.log('Dot clicked:', index + 1);
            goToSlide(index + 1);
        });

        // Hover event for dots
        dot.addEventListener('mouseenter', function() {
            console.log('Dot hovered:', index + 1);
            goToSlide(index + 1);
        });
    });

    console.log('Team slider initialized with', dots.length, 'dots');

    // Handle window resize
    window.addEventListener('resize', function() {
        if (isMobile()) {
            // Switch to mobile mode
            showMobileMember(currentMemberIndex);
        } else {
            // Switch to desktop mode
            const allMembers = document.querySelectorAll('.team-member');
            allMembers.forEach(member => {
                member.classList.remove('mobile-active');
            });

            // Reset to normal desktop slider
            const slider = document.querySelector('.team-slider');
            if (slider) {
                slider.style.transform = `translateX(-${currentSlideIndex * 100}%)`;
            }
        }
    });

    // Auto-slide disabled - manual navigation only
}

// FAQ Functionality
const faqItems = document.querySelectorAll('.faq-item');

faqItems.forEach(item => {
    const question = item.querySelector('.faq-question');

    if (question) {
        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');

            // Close all FAQ items
            faqItems.forEach(faqItem => {
                faqItem.classList.remove('active');
            });

            // Open clicked item if it wasn't active
            if (!isActive) {
                item.classList.add('active');
            }
        });
    }
});

// Pricing Tabs Functionality
function initializePricingTabs() {
    const pricingTabs = document.querySelectorAll('.pricing-tab');
    const pricingContents = document.querySelectorAll('.pricing-table-content');

    if (pricingTabs.length === 0 || pricingContents.length === 0) {
        return; // Exit if elements don't exist on this page
    }

    // Initialize: Hide all content except the first one
    pricingContents.forEach((content, index) => {
        if (index === 0) {
            content.classList.add('active');
        } else {
            content.classList.remove('active');
        }
    });

    // Ensure first tab is active
    pricingTabs.forEach((tab, index) => {
        if (index === 0) {
            tab.classList.add('active');
        } else {
            tab.classList.remove('active');
        }
    });

    pricingTabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
            e.preventDefault();
            const targetTab = tab.getAttribute('data-tab');

            // Remove active class from all tabs and contents
            pricingTabs.forEach(t => t.classList.remove('active'));
            pricingContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab and corresponding content
            tab.classList.add('active');
            const targetContent = document.getElementById(targetTab);
            if (targetContent) {
                targetContent.classList.add('active');
            }
        });
    });
}

// Initialize pricing tabs when DOM is ready
initializePricingTabs();

// Digital Marketing Animations
function initDigitalMarketingAnimations() {
    // Animate circular progress charts
    function animateCircularCharts() {
        const charts = document.querySelectorAll('.circular-chart');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const chart = entry.target;
                    const percentage = chart.getAttribute('data-percentage');
                    const circleFill = chart.querySelector('.circle-fill');

                    if (circleFill && percentage) {
                        // Calculate stroke-dashoffset based on percentage
                        const circumference = 377; // 2 * π * 60 (exact radius)
                        const offset = circumference - (circumference * percentage / 100);

                        // Add animation class and set the offset
                        chart.classList.add('animate');
                        circleFill.style.strokeDashoffset = offset;

                        // Animate the percentage number
                        const percentageElement = chart.querySelector('.percentage');
                        if (percentageElement) {
                            animateNumber(percentageElement, 0, percentage, 2000, '%');
                        }
                    }

                    observer.unobserve(chart);
                }
            });
        }, { threshold: 0.5 });

        charts.forEach(chart => observer.observe(chart));
    }

    // Animate bar charts
    function animateBarCharts() {
        const barFills = document.querySelectorAll('.bar-fill');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const barFill = entry.target;
                    const percentage = barFill.getAttribute('data-percentage');

                    if (percentage) {
                        setTimeout(() => {
                            barFill.classList.add('animate');
                            barFill.style.width = percentage + '%';
                        }, 300);
                    }

                    observer.unobserve(barFill);
                }
            });
        }, { threshold: 0.5 });

        barFills.forEach(barFill => observer.observe(barFill));
    }

    // Animate numbers
    function animateNumber(element, start, end, duration, suffix = '') {
        const startTime = performance.now();
        const endValue = parseInt(end);

        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function for smooth animation
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = Math.floor(start + (endValue - start) * easeOutQuart);

            element.textContent = currentValue + suffix;

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                element.textContent = endValue + suffix;
            }
        }

        requestAnimationFrame(updateNumber);
    }

    // Animate growth rate bars and agenda bars
    function animateGrowthBars() {
        const growthBars = document.querySelectorAll('.growth-bar, .agenda-bar');

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const bar = entry.target;
                    const barFill = bar.querySelector('.bar-fill');
                    const height = bar.dataset.height;

                    if (barFill && height) {
                        setTimeout(() => {
                            barFill.style.height = height + '%';
                        }, 300);
                    }

                    observer.unobserve(bar);
                }
            });
        }, { threshold: 0.3 });

        growthBars.forEach(bar => observer.observe(bar));
    }

    // Initialize animations
    if (document.querySelector('.circular-chart')) {
        animateCircularCharts();
    }

    if (document.querySelector('.bar-fill')) {
        animateBarCharts();
    }

    if (document.querySelector('.growth-bar, .agenda-bar')) {
        animateGrowthBars();
    }
}

// Initialize digital marketing animations
initDigitalMarketingAnimations();

// Export functions for use in other scripts
window.ShifturUtils = {
    showNotification
};
