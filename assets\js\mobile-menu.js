// Simple Mobile Menu Implementation
(function() {
    'use strict';
    
    console.log('Mobile menu script loaded');
    
    function initMobileMenu() {
        console.log('Initializing mobile menu...');
        
        const toggle = document.getElementById('mobileMenuToggle');
        const menu = document.getElementById('mobileMenu');
        const close = document.getElementById('mobileMenuClose');
        
        console.log('Elements found:', {
            toggle: !!toggle,
            menu: !!menu,
            close: !!close
        });
        
        if (!toggle || !menu) {
            console.error('Mobile menu elements not found');
            return;
        }
        
        function openMenu() {
            console.log('Opening menu');
            menu.style.display = 'block';
            menu.classList.add('active');
            toggle.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // Force styles
            setTimeout(() => {
                menu.style.transform = 'translateX(0)';
                menu.style.visibility = 'visible';
                menu.style.opacity = '1';
            }, 10);
        }
        
        function closeMenu() {
            console.log('Closing menu');
            menu.classList.remove('active');
            toggle.classList.remove('active');
            menu.style.transform = 'translateX(-100%)';
            menu.style.visibility = 'hidden';
            menu.style.opacity = '0';
            document.body.style.overflow = '';
            
            setTimeout(() => {
                menu.style.display = 'none';
            }, 300);
        }
        
        // Toggle button click
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Toggle clicked');
            
            if (menu.classList.contains('active')) {
                closeMenu();
            } else {
                openMenu();
            }
        });
        
        // Close button click
        if (close) {
            close.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                closeMenu();
            });
        }
        
        // Close when clicking outside
        document.addEventListener('click', function(e) {
            if (menu.classList.contains('active') && 
                !menu.contains(e.target) && 
                !toggle.contains(e.target)) {
                closeMenu();
            }
        });
        
        // Close on escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && menu.classList.contains('active')) {
                closeMenu();
            }
        });

        // Handle mobile dropdown toggles
        const dropdownToggles = document.querySelectorAll('.mobile-dropdown-toggle');
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const dropdown = this.parentElement;
                const isActive = dropdown.classList.contains('active');

                // Close all other dropdowns
                dropdownToggles.forEach(otherToggle => {
                    otherToggle.parentElement.classList.remove('active');
                });

                // Toggle current dropdown
                if (!isActive) {
                    dropdown.classList.add('active');
                }

                console.log('Dropdown toggled:', dropdown.classList.contains('active'));
            });
        });

        // Close menu when nav links are clicked
        const navLinks = document.querySelectorAll('.mobile-nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                setTimeout(closeMenu, 100);
            });
        });

        console.log('Mobile menu initialized');
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMobileMenu);
    } else {
        initMobileMenu();
    }
})();
