<?php
// Fix contact messages issue - real vs test messages
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Fix Contact Messages Issue</h1>";

require_once 'includes/db.php';

if (!$conn) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    exit;
}

echo "<p style='color: green;'>✅ Database connected</p>";

// Check current messages and their status
echo "<h2>Current Messages Analysis</h2>";

try {
    $all_messages = fetchAll($conn, "SELECT id, name, email, subject, status, created_at FROM contact_messages ORDER BY created_at DESC");
    
    echo "<p>Total messages in database: <strong>" . count($all_messages) . "</strong></p>";
    
    if (!empty($all_messages)) {
        echo "<h3>All Messages:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Subject</th><th>Status</th><th>Created</th><th>Source</th></tr>";
        
        foreach ($all_messages as $msg) {
            $status = $msg['status'] ?? 'NULL';
            $source = (strpos($msg['name'], 'Test') !== false || strpos($msg['subject'], 'Test') !== false) ? 'Test Form' : 'Website Form';
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($msg['id']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['name']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['email']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($msg['subject'], 0, 30)) . "...</td>";
            echo "<td style='color: " . ($status === 'NULL' || empty($status) ? 'red' : 'green') . ";'>" . htmlspecialchars($status) . "</td>";
            echo "<td>" . htmlspecialchars($msg['created_at']) . "</td>";
            echo "<td>" . htmlspecialchars($source) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Count messages by status
        $status_counts = ['NULL/Empty' => 0, 'new' => 0, 'read' => 0, 'replied' => 0, 'archived' => 0];
        foreach ($all_messages as $msg) {
            $status = $msg['status'] ?? '';
            if (empty($status) || $status === 'NULL') {
                $status_counts['NULL/Empty']++;
            } else {
                $status_counts[$status]++;
            }
        }
        
        echo "<h3>Status Distribution:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Status</th><th>Count</th></tr>";
        foreach ($status_counts as $status => $count) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($status) . "</td>";
            echo "<td>" . htmlspecialchars($count) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check for messages with NULL or empty status
        $null_status_messages = fetchAll($conn, "SELECT id, name, subject FROM contact_messages WHERE status IS NULL OR status = ''");
        
        if (!empty($null_status_messages)) {
            echo "<h3 style='color: red;'>⚠️ Messages with NULL/Empty Status:</h3>";
            echo "<p>These messages won't show properly in admin panel:</p>";
            echo "<ul>";
            foreach ($null_status_messages as $msg) {
                echo "<li>ID " . $msg['id'] . ": " . htmlspecialchars($msg['name']) . " - " . htmlspecialchars($msg['subject']) . "</li>";
            }
            echo "</ul>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

// Fix 1: Update existing messages with NULL status
echo "<h2>Fix 1: Update Messages with NULL Status</h2>";

if ($_POST && isset($_POST['fix_null_status'])) {
    try {
        $update_result = $conn->query("UPDATE contact_messages SET status = 'new' WHERE status IS NULL OR status = ''");
        if ($update_result) {
            $affected_rows = $conn->affected_rows;
            echo "<p style='color: green;'>✅ Updated $affected_rows messages to 'new' status</p>";
            echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
        } else {
            echo "<p style='color: red;'>❌ Failed to update messages: " . $conn->error . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error updating messages: " . $e->getMessage() . "</p>";
    }
}

// Fix 2: Update contact.php to include status
echo "<h2>Fix 2: Contact Form Code Issue</h2>";

$contact_file = 'contact.php';
if (file_exists($contact_file)) {
    $contact_content = file_get_contents($contact_file);
    
    // Check if status is being set
    if (strpos($contact_content, "'status' => 'new'") !== false) {
        echo "<p style='color: green;'>✅ Contact form already sets status field</p>";
    } else {
        echo "<p style='color: red;'>❌ Contact form does NOT set status field</p>";
        echo "<p>This is why website messages don't show in admin panel!</p>";
        
        if ($_POST && isset($_POST['fix_contact_form'])) {
            // Find the data array and add status
            $search = "'user_agent' => \$_SERVER['HTTP_USER_AGENT'] ?? ''";
            $replace = "'user_agent' => \$_SERVER['HTTP_USER_AGENT'] ?? '',\n                'status' => 'new'";
            
            $new_content = str_replace($search, $replace, $contact_content);
            
            if ($new_content !== $contact_content) {
                if (file_put_contents($contact_file, $new_content)) {
                    echo "<p style='color: green;'>✅ Contact form updated to include status field!</p>";
                    echo "<p>New contact form submissions will now appear in admin panel</p>";
                } else {
                    echo "<p style='color: red;'>❌ Failed to update contact form file</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ Could not find the right place to add status field</p>";
            }
        }
    }
} else {
    echo "<p style='color: red;'>❌ contact.php file not found</p>";
}

// Test the admin messages query
echo "<h2>Test Admin Messages Query</h2>";

try {
    // This is the exact query from admin/messages.php
    $admin_messages = fetchAll($conn, "SELECT * FROM contact_messages ORDER BY created_at DESC");
    echo "<p>Admin query returned: <strong>" . count($admin_messages) . "</strong> messages</p>";
    
    if (!empty($admin_messages)) {
        echo "<p style='color: green;'>✅ Admin query working correctly</p>";
        echo "<p>First message: " . htmlspecialchars($admin_messages[0]['name']) . " - " . htmlspecialchars($admin_messages[0]['subject']) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Admin query returned no results</p>";
    }
    
    // Test statistics calculation
    $message_stats = ['new' => 0, 'read' => 0, 'replied' => 0, 'archived' => 0];
    foreach ($admin_messages as $message) {
        $status = $message['status'] ?? 'new';
        if (isset($message_stats[$status])) {
            $message_stats[$status]++;
        }
    }
    
    echo "<h3>Admin Panel Statistics:</h3>";
    foreach ($message_stats as $status => $count) {
        echo "<p>$status: $count</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error testing admin query: " . $e->getMessage() . "</p>";
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Fix Contact Messages</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 5px; }
        .btn:hover { background: #005a87; }
        .btn-warning { background: #f39c12; }
        .btn-warning:hover { background: #e67e22; }
        .fix-section { background: #f9f9f9; padding: 15px; border-radius: 8px; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="fix-section">
        <h3>🔧 Fix 1: Update Existing Messages</h3>
        <p>Update all messages with NULL/empty status to 'new' status so they appear in admin panel:</p>
        <form method="POST">
            <button type="submit" name="fix_null_status" class="btn btn-warning">Fix NULL Status Messages</button>
        </form>
    </div>
    
    <div class="fix-section">
        <h3>🔧 Fix 2: Update Contact Form Code</h3>
        <p>Modify contact.php to include status field for future submissions:</p>
        <form method="POST">
            <button type="submit" name="fix_contact_form" class="btn btn-warning">Fix Contact Form Code</button>
        </form>
    </div>
    
    <h2>Test After Fixes</h2>
    <p>
        <a href="contact.php" class="btn">Test Contact Form</a>
        <a href="admin/messages.php" class="btn">Check Admin Messages</a>
        <a href="admin/dashboard.php" class="btn">Admin Dashboard</a>
    </p>
    
    <h2>What This Fix Does</h2>
    <ol>
        <li>✅ <strong>Identifies the problem:</strong> Real contact form doesn't set 'status' field</li>
        <li>✅ <strong>Updates existing messages:</strong> Sets status = 'new' for all NULL status messages</li>
        <li>✅ <strong>Fixes contact form:</strong> Adds status field to future submissions</li>
        <li>✅ <strong>Verifies admin query:</strong> Tests the exact query used in admin panel</li>
    </ol>
    
    <h2>Expected Results</h2>
    <ul>
        <li>🎯 All existing website messages will appear in admin panel</li>
        <li>🎯 New contact form submissions will include status field</li>
        <li>🎯 Admin panel statistics will show correct counts</li>
        <li>🎯 Message management features will work properly</li>
    </ul>
    
    <p style="color: red;"><strong>⚠️ Delete this fix file after use!</strong></p>
</body>
</html>
