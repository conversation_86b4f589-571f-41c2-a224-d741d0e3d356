<?php
// Debug contact messages issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Contact Messages Debug - shiftur.co</h1>";

// Include database connection
try {
    require_once 'includes/db.php';
    echo "<p style='color: green;'>✅ Database connection loaded</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
    exit;
}

if (!$conn) {
    echo "<p style='color: red;'>❌ No database connection</p>";
    exit;
}

echo "<p style='color: green;'>✅ Database connected to: " . htmlspecialchars($db_name) . "</p>";

// Check if contact_messages table exists
echo "<h2>Contact Messages Table Check</h2>";

try {
    $table_check = $conn->query("SHOW TABLES LIKE 'contact_messages'");
    if ($table_check && $table_check->num_rows > 0) {
        echo "<p style='color: green;'>✅ contact_messages table exists</p>";
        
        // Get table structure
        $structure = $conn->query("DESCRIBE contact_messages");
        if ($structure) {
            echo "<h3>Table Structure:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            while ($row = $structure->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($row['Extra'] ?? '') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Check existing messages
        $count_result = $conn->query("SELECT COUNT(*) as count FROM contact_messages");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo "<p>Messages in database: <strong>$count</strong></p>";
            
            if ($count > 0) {
                echo "<h3>Recent Messages (Latest 5):</h3>";
                $recent = $conn->query("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 5");
                if ($recent) {
                    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                    echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Subject</th><th>Message</th><th>Status</th><th>Created</th></tr>";
                    while ($row = $recent->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['id']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['name'] ?? 'N/A') . "</td>";
                        echo "<td>" . htmlspecialchars($row['email'] ?? 'N/A') . "</td>";
                        echo "<td>" . htmlspecialchars(substr($row['subject'] ?? 'N/A', 0, 30)) . "...</td>";
                        echo "<td>" . htmlspecialchars(substr($row['message'] ?? 'N/A', 0, 50)) . "...</td>";
                        echo "<td>" . htmlspecialchars($row['status'] ?? 'new') . "</td>";
                        echo "<td>" . htmlspecialchars($row['created_at'] ?? 'N/A') . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
                
                // Check status distribution
                echo "<h3>Status Distribution:</h3>";
                $status_dist = $conn->query("SELECT status, COUNT(*) as count FROM contact_messages GROUP BY status");
                if ($status_dist) {
                    echo "<table border='1' style='border-collapse: collapse;'>";
                    echo "<tr><th>Status</th><th>Count</th></tr>";
                    while ($row = $status_dist->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['status'] ?? 'NULL') . "</td>";
                        echo "<td>" . htmlspecialchars($row['count']) . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ No messages found in database</p>";
            }
        }
        
        // Check if status column exists
        $status_check = $conn->query("SHOW COLUMNS FROM contact_messages LIKE 'status'");
        if (!$status_check || $status_check->num_rows == 0) {
            echo "<p style='color: orange;'>⚠️ Status column missing - adding it...</p>";
            $add_status = $conn->query("ALTER TABLE contact_messages ADD COLUMN status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new'");
            if ($add_status) {
                echo "<p style='color: green;'>✅ Status column added successfully</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to add status column: " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color: green;'>✅ Status column exists</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ contact_messages table does not exist</p>";
        echo "<p>Creating contact_messages table...</p>";
        
        // Create contact_messages table
        $create_sql = "
        CREATE TABLE contact_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            company VARCHAR(100),
            subject VARCHAR(200) NOT NULL,
            message TEXT NOT NULL,
            service_interest VARCHAR(100),
            business_type VARCHAR(50),
            ip_address VARCHAR(45),
            user_agent TEXT,
            status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if ($conn->query($create_sql)) {
            echo "<p style='color: green;'>✅ contact_messages table created successfully</p>";
        } else {
            echo "<p style='color: red;'>❌ Error creating table: " . $conn->error . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking table: " . $e->getMessage() . "</p>";
}

// Test contact form submission
echo "<h2>Test Contact Form Submission</h2>";

if ($_POST && isset($_POST['test_contact'])) {
    $test_data = [
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'phone' => '******-0123',
        'company' => 'Test Company',
        'subject' => 'Test Message from Debug Script',
        'message' => 'This is a test message to verify the contact form is working properly.',
        'service_interest' => 'Web Development',
        'business_type' => 'Small Business',
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Debug Script',
        'status' => 'new'
    ];
    
    try {
        $result = insertRecord($conn, 'contact_messages', $test_data);
        if ($result) {
            echo "<p style='color: green;'>✅ Test message created successfully! ID: $result</p>";
            echo "<script>setTimeout(function(){ window.location.reload(); }, 2000);</script>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error creating test message: " . $e->getMessage() . "</p>";
    }
}

// Test admin messages page query
echo "<h2>Admin Messages Page Query Test</h2>";

try {
    // Test the exact query used in admin messages page
    $messages = fetchAll($conn, "SELECT * FROM contact_messages ORDER BY created_at DESC");
    echo "<p>Query result: " . count($messages) . " messages found</p>";
    
    if (!empty($messages)) {
        echo "<p style='color: green;'>✅ Messages query successful</p>";
        echo "<p>First message: " . htmlspecialchars($messages[0]['name'] ?? 'N/A') . " - " . htmlspecialchars($messages[0]['subject'] ?? 'N/A') . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Query returned no results</p>";
    }
    
    // Test statistics query
    $stats = ['new' => 0, 'read' => 0, 'replied' => 0, 'archived' => 0];
    foreach ($messages as $message) {
        $status = $message['status'] ?? 'new';
        if (isset($stats[$status])) {
            $stats[$status]++;
        }
    }
    
    echo "<h3>Statistics:</h3>";
    foreach ($stats as $status => $count) {
        echo "<p>$status: $count</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error testing query: " . $e->getMessage() . "</p>";
}

// Check admin messages page file
echo "<h2>Admin Messages Page Check</h2>";

if (file_exists('admin/messages.php')) {
    echo "<p style='color: green;'>✅ admin/messages.php exists</p>";
    
    // Check if we can read the file
    $file_content = file_get_contents('admin/messages.php');
    if ($file_content !== false) {
        echo "<p style='color: green;'>✅ admin/messages.php is readable</p>";
        
        // Check for key functions
        if (strpos($file_content, 'contact_messages') !== false) {
            echo "<p style='color: green;'>✅ File references contact_messages table</p>";
        } else {
            echo "<p style='color: red;'>❌ File does not reference contact_messages table</p>";
        }
        
        if (strpos($file_content, 'fetchAll') !== false) {
            echo "<p style='color: green;'>✅ File uses fetchAll function</p>";
        } else {
            echo "<p style='color: red;'>❌ File does not use fetchAll function</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Cannot read admin/messages.php</p>";
    }
} else {
    echo "<p style='color: red;'>❌ admin/messages.php does not exist</p>";
}

// Check contact form page
echo "<h2>Contact Form Page Check</h2>";

if (file_exists('contact.php')) {
    echo "<p style='color: green;'>✅ contact.php exists</p>";
    
    $contact_content = file_get_contents('contact.php');
    if ($contact_content !== false) {
        if (strpos($contact_content, 'contact_messages') !== false) {
            echo "<p style='color: green;'>✅ Contact form saves to contact_messages table</p>";
        } else {
            echo "<p style='color: red;'>❌ Contact form does not save to contact_messages table</p>";
        }
        
        if (strpos($contact_content, 'insertRecord') !== false) {
            echo "<p style='color: green;'>✅ Contact form uses insertRecord function</p>";
        } else {
            echo "<p style='color: red;'>❌ Contact form does not use insertRecord function</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ contact.php does not exist</p>";
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Contact Messages Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; margin: 10px 0; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 0; }
        .btn:hover { background: #005a87; }
    </style>
</head>
<body>
    <h2>Test Contact Message Creation</h2>
    <form method="POST">
        <button type="submit" name="test_contact" class="btn">Create Test Message</button>
    </form>
    
    <h2>Quick Links</h2>
    <p>
        <a href="contact.php">Contact Form</a> | 
        <a href="admin/messages.php">Admin Messages</a> | 
        <a href="admin/dashboard.php">Admin Dashboard</a>
    </p>
    
    <h2>Common Issues & Solutions</h2>
    <ul>
        <li><strong>Missing Table:</strong> Script will create contact_messages table</li>
        <li><strong>Missing Status Column:</strong> Script will add status column</li>
        <li><strong>No Messages:</strong> Use test button to create sample message</li>
        <li><strong>Query Issues:</strong> Check if fetchAll function works</li>
        <li><strong>File Permissions:</strong> Ensure admin files are readable</li>
    </ul>
    
    <h2>Next Steps</h2>
    <ol>
        <li>✅ Verify table exists and has correct structure</li>
        <li>🔄 Create test message to verify insertion works</li>
        <li>🔄 Check admin messages page shows the test message</li>
        <li>🔄 Test contact form submission from website</li>
        <li>⚠️ Delete this debug file after use</li>
    </ol>
    
    <p style="color: red;"><strong>⚠️ Delete this debug file after debugging!</strong></p>
</body>
</html>
