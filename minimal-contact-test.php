<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Minimal Contact Test</h1>";

// Direct database connection test
require_once 'includes/db.php';

echo "<h2>Database Connection Test</h2>";
if ($conn) {
    echo "<p style='color: green;'>✅ Database connected</p>";
    
    // Test if contact_messages table exists
    $result = $conn->query("SHOW TABLES LIKE 'contact_messages'");
    if ($result && $result->num_rows > 0) {
        echo "<p style='color: green;'>✅ contact_messages table exists</p>";
        
        // Show table structure
        $columns = $conn->query("DESCRIBE contact_messages");
        echo "<h3>Table Structure:</h3><ul>";
        while ($row = $columns->fetch_assoc()) {
            echo "<li>" . $row['Field'] . " - " . $row['Type'] . "</li>";
        }
        echo "</ul>";
        
        // Count existing messages
        $count = $conn->query("SELECT COUNT(*) as count FROM contact_messages")->fetch_assoc()['count'];
        echo "<p>Current messages: <strong>$count</strong></p>";
        
    } else {
        echo "<p style='color: red;'>❌ contact_messages table missing</p>";
        
        // Create the table
        $create_sql = "
        CREATE TABLE contact_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            company VARCHAR(100),
            subject VARCHAR(200) NOT NULL,
            message TEXT NOT NULL,
            service_interest VARCHAR(100),
            business_type VARCHAR(50),
            ip_address VARCHAR(45),
            user_agent TEXT,
            status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if ($conn->query($create_sql)) {
            echo "<p style='color: green;'>✅ Table created successfully</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create table: " . $conn->error . "</p>";
        }
    }
} else {
    echo "<p style='color: red;'>❌ No database connection</p>";
    exit;
}

// Test form submission
if ($_POST && isset($_POST['submit'])) {
    echo "<h2>Form Submission Test</h2>";
    
    $name = $_POST['name'] ?? '';
    $email = $_POST['email'] ?? '';
    $subject = $_POST['subject'] ?? '';
    $message = $_POST['message'] ?? '';
    $business_type = $_POST['business_type'] ?? '';
    
    echo "<p>Received data:</p>";
    echo "<ul>";
    echo "<li>Name: " . htmlspecialchars($name) . "</li>";
    echo "<li>Email: " . htmlspecialchars($email) . "</li>";
    echo "<li>Subject: " . htmlspecialchars($subject) . "</li>";
    echo "<li>Business Type: " . htmlspecialchars($business_type) . "</li>";
    echo "</ul>";
    
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        echo "<p style='color: red;'>❌ Missing required fields</p>";
    } else {
        // Direct SQL insert (bypassing functions for testing)
        $stmt = $conn->prepare("INSERT INTO contact_messages (name, email, subject, message, business_type, status, created_at) VALUES (?, ?, ?, ?, ?, 'new', NOW())");
        
        if ($stmt) {
            $stmt->bind_param("sssss", $name, $email, $subject, $message, $business_type);
            
            if ($stmt->execute()) {
                $insert_id = $conn->insert_id;
                echo "<p style='color: green;'>✅ Message saved successfully! ID: $insert_id</p>";
                
                // Verify the insert
                $verify = $conn->query("SELECT * FROM contact_messages WHERE id = $insert_id");
                if ($verify && $verify->num_rows > 0) {
                    $row = $verify->fetch_assoc();
                    echo "<p style='color: green;'>✅ Verified in database: " . htmlspecialchars($row['name']) . "</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ Execute failed: " . $stmt->error . "</p>";
            }
            $stmt->close();
        } else {
            echo "<p style='color: red;'>❌ Prepare failed: " . $conn->error . "</p>";
        }
    }
}

// Show recent messages
if ($conn) {
    $recent = $conn->query("SELECT * FROM contact_messages ORDER BY created_at DESC LIMIT 5");
    if ($recent && $recent->num_rows > 0) {
        echo "<h3>Recent Messages:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Subject</th><th>Business Type</th><th>Created</th></tr>";
        while ($row = $recent->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['email']) . "</td>";
            echo "<td>" . htmlspecialchars($row['subject']) . "</td>";
            echo "<td>" . htmlspecialchars($row['business_type'] ?? 'N/A') . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Minimal Contact Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input, select, textarea { width: 300px; padding: 5px; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <h2>Test Form</h2>
    <form method="POST">
        <div class="form-group">
            <label>Name *</label>
            <input type="text" name="name" value="Test User <?php echo date('H:i:s'); ?>" required>
        </div>
        <div class="form-group">
            <label>Email *</label>
            <input type="email" name="email" value="test<?php echo time(); ?>@example.com" required>
        </div>
        <div class="form-group">
            <label>Subject *</label>
            <input type="text" name="subject" value="Test Message <?php echo date('H:i:s'); ?>" required>
        </div>
        <div class="form-group">
            <label>Business Type</label>
            <select name="business_type">
                <option value="">Select type</option>
                <option value="Startup">Startup</option>
                <option value="Small Business" selected>Small Business</option>
                <option value="Enterprise">Enterprise</option>
            </select>
        </div>
        <div class="form-group">
            <label>Message *</label>
            <textarea name="message" required>This is a minimal test message to verify database insertion.</textarea>
        </div>
        <button type="submit" name="submit" class="btn">Submit Test</button>
    </form>
    
    <p><a href="contact.php" class="btn">Go to Real Contact Form</a></p>
</body>
</html>
