<?php
// Debug admin login issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Admin Login Debug - shiftur.co</h1>";

// Check database connection
try {
    require_once 'includes/db.php';
    echo "<p style='color: green;'>✅ Database connection successful</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database connection failed: " . $e->getMessage() . "</p>";
    exit;
}

if (!$conn) {
    echo "<p style='color: red;'>❌ No database connection available</p>";
    exit;
}

// Check if admin-auth.php exists and loads
try {
    require_once 'includes/admin-auth.php';
    echo "<p style='color: green;'>✅ admin-auth.php loaded successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error loading admin-auth.php: " . $e->getMessage() . "</p>";
    exit;
}

// Check if admin_users table exists
echo "<h2>Admin Users Table Check</h2>";
try {
    $table_check = $conn->query("SHOW TABLES LIKE 'admin_users'");
    if ($table_check && $table_check->num_rows > 0) {
        echo "<p style='color: green;'>✅ admin_users table exists</p>";
        
        // Get table structure
        $structure = $conn->query("DESCRIBE admin_users");
        if ($structure) {
            echo "<h3>Table Structure:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($row = $structure->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Check existing admin users
        $count_result = $conn->query("SELECT COUNT(*) as count FROM admin_users");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo "<p>Admin users in database: <strong>$count</strong></p>";
            
            if ($count > 0) {
                echo "<h3>Existing Admin Users:</h3>";
                $users = $conn->query("SELECT id, username, email, first_name, last_name, role, status, created_at FROM admin_users");
                if ($users) {
                    echo "<table border='1' style='border-collapse: collapse;'>";
                    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Role</th><th>Status</th><th>Created</th></tr>";
                    while ($user = $users->fetch_assoc()) {
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($user['id']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['role']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['status']) . "</td>";
                        echo "<td>" . htmlspecialchars($user['created_at']) . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ No admin users found - creating default admin user...</p>";
                
                // Create default admin user
                try {
                    $default_admin = [
                        'username' => 'admin',
                        'email' => '<EMAIL>',
                        'password' => 'admin123',
                        'first_name' => 'Admin',
                        'last_name' => 'User',
                        'role' => 'super_admin',
                        'status' => 'active'
                    ];
                    
                    $result = createAdminUser($default_admin);
                    if ($result['success']) {
                        echo "<p style='color: green;'>✅ Default admin user created successfully!</p>";
                        echo "<p><strong>Login Credentials:</strong></p>";
                        echo "<p>Email: <EMAIL></p>";
                        echo "<p>Password: admin123</p>";
                    } else {
                        echo "<p style='color: red;'>❌ Failed to create admin user: " . $result['message'] . "</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Error creating admin user: " . $e->getMessage() . "</p>";
                }
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ admin_users table does not exist</p>";
        echo "<p>Creating admin_users table...</p>";
        
        // Create admin_users table
        $create_sql = "
        CREATE TABLE admin_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            role ENUM('super_admin', 'admin', 'editor', 'viewer') DEFAULT 'admin',
            status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if ($conn->query($create_sql)) {
            echo "<p style='color: green;'>✅ admin_users table created successfully</p>";
            
            // Create default admin user
            $default_admin = [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => 'admin123',
                'first_name' => 'Admin',
                'last_name' => 'User',
                'role' => 'super_admin',
                'status' => 'active'
            ];
            
            $result = createAdminUser($default_admin);
            if ($result['success']) {
                echo "<p style='color: green;'>✅ Default admin user created!</p>";
                echo "<p><strong>Login Credentials:</strong></p>";
                echo "<p>Email: <EMAIL></p>";
                echo "<p>Password: admin123</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Error creating table: " . $conn->error . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking admin table: " . $e->getMessage() . "</p>";
}

// Test admin authentication
echo "<h2>Test Admin Authentication</h2>";

if ($_POST && isset($_POST['test_login'])) {
    $test_username = $_POST['username'] ?? '';
    $test_password = $_POST['password'] ?? '';
    
    echo "<p>Testing login with:</p>";
    echo "<p>Username: " . htmlspecialchars($test_username) . "</p>";
    
    try {
        $user = authenticateAdminUser($test_username, $test_password);
        
        if ($user) {
            echo "<p style='color: green;'>✅ Authentication successful!</p>";
            echo "<p>User ID: " . $user['id'] . "</p>";
            echo "<p>Name: " . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . "</p>";
            echo "<p>Role: " . htmlspecialchars($user['role']) . "</p>";
            echo "<p>Status: " . htmlspecialchars($user['status']) . "</p>";
            
            // Test login function
            session_start();
            loginAdminUser($user);
            echo "<p style='color: green;'>✅ Login function executed successfully</p>";
            echo "<p>Session admin_user_id: " . ($_SESSION['admin_user_id'] ?? 'Not set') . "</p>";
            
        } else {
            echo "<p style='color: red;'>❌ Authentication failed</p>";
            echo "<p>Check username/email and password</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error during authentication: " . $e->getMessage() . "</p>";
    }
}

// Check admin login page
echo "<h2>Admin Login Page Check</h2>";
if (file_exists('admin/login.php')) {
    echo "<p style='color: green;'>✅ admin/login.php exists</p>";
} else {
    echo "<p style='color: red;'>❌ admin/login.php missing</p>";
}

if (file_exists('admin/dashboard.php')) {
    echo "<p style='color: green;'>✅ admin/dashboard.php exists</p>";
} else {
    echo "<p style='color: red;'>❌ admin/dashboard.php missing</p>";
}

// Check PHP functions
echo "<h2>PHP Functions Check</h2>";
$functions = ['password_hash', 'password_verify', 'session_start', 'mysqli_connect'];
foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✅ $func available</p>";
    } else {
        echo "<p style='color: red;'>❌ $func missing</p>";
    }
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Admin Login Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 0; }
        .btn:hover { background: #005a87; }
        .form-group { margin: 10px 0; }
        .form-group label { display: block; margin-bottom: 5px; }
        .form-group input { padding: 8px; width: 300px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h2>Test Admin Login</h2>
    <form method="POST">
        <div class="form-group">
            <label>Username/Email:</label>
            <input type="text" name="username" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label>Password:</label>
            <input type="password" name="password" value="admin123" required>
        </div>
        <button type="submit" name="test_login" class="btn">Test Login</button>
    </form>
    
    <h2>Quick Links</h2>
    <p>
        <a href="admin/login.php">Admin Login Page</a> | 
        <a href="admin/dashboard.php">Admin Dashboard</a> | 
        <a href="signup.php">Website Signup</a>
    </p>
    
    <h2>Common Admin Login Issues</h2>
    <ul>
        <li><strong>Missing admin_users table:</strong> Fixed by this script</li>
        <li><strong>No admin users:</strong> Default admin created</li>
        <li><strong>Wrong credentials:</strong> Use <EMAIL> / admin123</li>
        <li><strong>Session issues:</strong> Check if sessions are working</li>
        <li><strong>File permissions:</strong> Ensure admin files are readable</li>
    </ul>
    
    <p style="color: red;"><strong>⚠️ Delete this debug file after use for security!</strong></p>
</body>
</html>
