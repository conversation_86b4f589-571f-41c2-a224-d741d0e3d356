/* ===== SHIFTUR LIGHT THEME - MAIN STYLESHEET ===== */

/* CSS Variables */
:root {
    /* Light Theme Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: #ffffff;
    --bg-hover: #f8fafc;
    
    /* Brand Colors - Purple & Orange */
    --purple-primary: #8b5cf6;
    --purple-secondary: #a855f7;
    --purple-light: #c4b5fd;
    --orange-primary: #f97316;
    --orange-secondary: #fb923c;
    --orange-light: #fed7aa;
    
    /* Text Colors */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-white: #ffffff;
    
    /* Border Colors */
    --border-primary: #e2e8f0;
    --border-secondary: #cbd5e1;
    --border-accent: #8b5cf6;
    
    /* Shadow */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-purple: 0 10px 25px rgba(139, 92, 246, 0.15);
    --shadow-orange: 0 10px 25px rgba(249, 115, 22, 0.15);
    
    /* Transitions */
    --transition-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-display: 'Poppins', 'Inter', sans-serif;
    
    /* Spacing */
    --container-padding: 2rem;
    --section-padding: 6rem 0;
    --border-radius: 12px;
    --border-radius-lg: 20px;
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: 600;
    line-height: 1.2;
    color: var(--text-primary);
}

.text-purple {
    color: var(--purple-primary);
}

.text-orange {
    color: var(--orange-primary);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-normal);
    border: none;
    font-size: 0.95rem;
}

.btn-primary {
    background-color: var(--purple-primary);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    background-color: var(--purple-secondary);
    box-shadow: var(--shadow-purple);
}

.btn-outline {
     background-color: var(--purple-primary);
 color: var(--text-white);
    border: 2px solid var(--purple-primary);
}

.btn-outline:hover {
        background-color: var(--purple-secondary);
    color: var(--text-white);
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* Header */
.header {
    background-color: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid var(--border-primary);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    transition: var(--transition-normal);
}

.header.scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
    max-width: 1200px;
    margin: 0 auto;
    padding-left: 0.5rem;
    padding-right: 2rem;
}

.logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    transition: var(--transition-normal);
}

.logo-img {
    height: 35px;
    width: auto;
    max-width: 150px;
    transition: var(--transition-normal);
}

.logo:hover .logo-img {
    transform: scale(1.05);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0;
    margin: 0;
    padding: 0;
    margin-left: 4rem;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    position: relative;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-normal);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover,
.nav-link.active {
    color: var(--purple-primary);
    background-color: var(--bg-hover);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-left: 3rem;
}

/* Hero Section */
.hero {
    padding: 6rem 0 3rem;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(ellipse at center, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
    height: 350px;
    width: 170%;
    margin-left: -10%;
}

/* Hide the purple Digital Solutions box globally */
.hero-main-visual {
    display: none !important;
}

.floating-card {
    position: absolute;
    background: transparent !important;
    background-color: transparent !important;
    border: none !important;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    box-shadow: none !important;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    animation: float 6s ease-in-out infinite;
}

.floating-card i {
    font-size: 1.2rem;
    color: var(--purple-primary);
}

.floating-card span {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
}

/* Ensure no shadows or backgrounds on floating cards in any state */
.floating-card,
.floating-card:hover,
.floating-card:focus,
.floating-card:active,
.floating-card:before,
.floating-card:after {
    box-shadow: none !important;
    filter: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    text-shadow: none !important;
    drop-shadow: none !important;
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    border: none !important;
}

/* Override any AOS or external library shadows and backgrounds */
.floating-card[data-aos],
.floating-card.aos-animate {
    box-shadow: none !important;
    filter: none !important;
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
    border: none !important;
}

.card-0 {
    top: 5%;
    left: 20%;
    animation-delay: 5s;
}

.card-1 {
    top: 35%;
    left: 10%;
    animation-delay: 0s;
}

.card-2 {
    top: 75%;
    left: 5%;
    animation-delay: 2s;
}

.card-3 {
    top: 10%;
    right: 20%;
    animation-delay: 4s;
}

.card-4 {
    top: 80%;
    right: 15%;
    animation-delay: 1s;
}

.card-5 {
    top: 50%;
    right: 10%;
    animation-delay: 3s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Services Section */
.services {
    padding: var(--section-padding);
    background-color: var(--bg-secondary);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 600;
    margin-bottom: 1rem;
}

.section-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

/* AI Services Grid - 4 items in one row */
.ai-services-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
}

/* Responsive for AI services grid */
@media (max-width: 1200px) {
    .ai-services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .ai-services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

.service-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--purple-primary);
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-accent);
}

.service-icon {
    width: 60px;
    height: 60px;
    background: var(--purple-primary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.service-icon i {
    font-size: 1.5rem;
    color: white;
}

.service-card:hover .service-icon {
    transform: translateY(-8px) scale(1.1);
}

.service-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.service-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
    margin-bottom: 2rem;
}

.service-features li {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-features li::before {
    content: '✓';
    color: var(--orange-primary);
    font-weight: bold;
}

.service-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--purple-primary);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition-normal);
}

.service-link:hover {
    color: var(--purple-secondary);
    transform: translateX(5px);
}

/* About Section */
.about {
    padding: 3rem 0;
    background-color: var(--bg-primary);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.about-features {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.feature-item i {
    color: var(--orange-primary);
    font-size: 1.1rem;
}

.feature-item span {
    font-weight: 500;
    color: var(--text-primary);
}

.about-visual {
    position: relative;
    height: 400px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-stats {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3rem;
    margin-top: 7rem;
    flex-wrap: nowrap;
    text-align: center;
    width: 100%;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.stats-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--purple-primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Hero Stats Specific Styles */
.hero-stats .stat-item {
    flex: 1;
    min-width: 150px;
    text-align: center;
}

.hero-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--purple-primary);
    margin-bottom: 0.5rem;
    line-height: 1.2;
    white-space: nowrap;
}

.hero-stats .stat-label {
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 600;
    white-space: nowrap;
}

/* CTA Section */
.cta {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--purple-primary) 0%, var(--purple-secondary) 100%);
    text-align: center;
    color: var(--text-white);
}

.cta-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    margin-bottom: 1rem;
    color: var(--text-white);
}

.cta-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta .btn-primary {
    background-color: var(--text-white);
    color: var(--purple-primary);
}

.cta .btn-primary:hover {
    background-color: var(--bg-secondary);
}

.cta .btn-outline {
    border-color: var(--text-white);
    color: var(--text-white);
}

.cta .btn-outline:hover {
    background-color: var(--text-white);
    color: var(--purple-primary);
}

/* Testimonials Section */
.testimonials {
    padding: var(--section-padding);
    background-color: var(--bg-secondary);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    transition: var(--transition-normal);
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--purple-primary);
}

.testimonial-rating {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.testimonial-rating i {
    color: #fbbf24;
    font-size: 1rem;
}

.testimonial-text {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 2rem;
    font-style: italic;
    position: relative;
    padding-left: 1rem;
    padding-top: 0.5rem;
}

.testimonial-text::before {
    content: '"';
    font-size: 2.5rem;
    color: var(--purple-primary);
    position: absolute;
    top: -0.5rem;
    left: -0.25rem;
    font-family: serif;
    z-index: 1;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: var(--purple-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--purple-primary);
    font-size: 1.2rem;
}

.author-info h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.author-info span {
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--purple-primary);
    color: var(--text-white);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--transition-normal);
    z-index: 1000;
    box-shadow: var(--shadow-lg);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: var(--purple-secondary);
    transform: translateY(-2px);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 1001;
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

.notification-success {
    border-left: 4px solid #16a34a;
}

.notification-error {
    border-left: 4px solid #dc2626;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    margin-left: auto;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Form Error States */
.form-group input.error,
.form-group textarea.error {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* Page Header Styles */
.page-header {
    padding: 10rem 0 4rem;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    text-align: center;
    position: relative;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(ellipse at center, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

.page-header-content {
    position: relative;
    z-index: 2;
}

.page-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.page-description {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto 2rem;
    line-height: 1.6;
}

.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.breadcrumb a {
    color: var(--purple-primary);
    text-decoration: none;
    transition: var(--transition-normal);
}

.breadcrumb a:hover {
    color: var(--purple-secondary);
}

/* MVV Section (Mission, Vision, Values) */
.mvv-section {
    padding: var(--section-padding);
    background: var(--bg-primary);
}

.mvv-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.mvv-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    transition: var(--transition-normal);
}

.mvv-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--purple-primary);
}

.mvv-icon {
    width: 80px;
    height: 80px;
    background: var(--purple-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.mvv-card:hover .mvv-icon {
    transform: translateY(-8px) scale(1.1);
}

.mvv-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.mvv-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* About Content */
.about-content {
    padding: 6rem 0;
    background: var(--bg-secondary);
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.content-grid.content-centered {
    grid-template-columns: 1fr;
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
    padding: 2rem;
}

.content-grid.content-centered .content-text {
    margin: 0 auto;
}

.content-text p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.content-features {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-top: 2rem;
}

.content-centered .content-features {
    max-width: 700px;
    margin: 3rem auto 0;
    justify-items: center;
}

.content-features .feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.content-features .feature-item i {
    color: var(--orange-primary);
    font-size: 1.1rem;
}

.image-placeholder {
    height: 400px;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    border: 2px dashed var(--border-primary);
}

.image-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.image-placeholder span {
    font-size: 1.2rem;
    font-weight: 600;
}

/* Team Section */
.team-section {
    padding: var(--section-padding);
    background: var(--bg-primary);
}

.team-slider-container {
    position: relative;
    margin-top: 3rem;
    overflow: hidden;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

.team-slider {
    display: flex;
    transition: transform 0.5s ease-in-out;
}

.team-slide {
    min-width: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 0 2rem;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.team-member {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    transition: var(--transition-normal);
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--purple-primary);
}

.member-avatar {
    width: 100px;
    height: 100px;
    background: var(--purple-light);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: var(--purple-primary);
    font-size: 2.5rem;
}

.member-info h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.member-role {
    color: var(--orange-primary);
    font-weight: 600;
    margin-bottom: 1rem;
    display: block;
}

.member-bio {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.member-social {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.member-social a {
    width: 40px;
    height: 40px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-normal);
}

.member-social a:hover {
    background: var(--purple-primary);
    color: var(--text-white);
    border-color: var(--purple-primary);
}

/* Slider Navigation */
.slider-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 1rem;
    pointer-events: none;
    transform: translateY(-50%);
}

.slider-btn {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: all;
    box-shadow: var(--shadow-md);
    color: var(--text-primary);
    outline: none;
}

.slider-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--purple-primary);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.slider-btn i {
    font-size: 1.2rem;
}

/* Slider Dots */
.slider-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--border-primary);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: var(--purple-primary);
    transform: scale(1.2);
}

.dot:hover {
    background: var(--purple-primary);
    transform: scale(1.3);
    box-shadow: 0 2px 8px rgba(139, 69, 255, 0.3);
}

/* Why Choose Us */
.why-choose-us {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    transition: var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border: 2px solid var(--orange-primary);
}

.feature-card:hover .feature-icon {
    transform: translateY(-8px) scale(1.1);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: var(--orange-primary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.8rem;
    transition: all 0.3s ease;
}

.feature-card h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== MOBILE RESPONSIVE DESIGN - COMPLETE REWRITE ===== */

/* Mobile First - All devices under 768px */
@media (max-width: 768px) {
    /* ROOT LEVEL: Absolutely prevent horizontal scrolling */
    :root {
        overflow-x: hidden !important;
    }
    /* CRITICAL: Prevent horizontal scrolling on all mobile devices */
    * {
        box-sizing: border-box !important;
        max-width: 100vw !important;
    }

    /* Ultra-aggressive horizontal scroll prevention */
    *:not(html):not(body) {
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    /* Specific element targeting */
    div, section, article, main, header, footer, nav, aside,
    .container, .wrapper, .content, .hero, .service-hero,
    .hero-content, .service-hero-content, .hero-visual, .content-visual {
        max-width: 100% !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
    }

    html {
        overflow-x: hidden !important;
        width: 100% !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    body {
        overflow-x: hidden !important;
        width: 100% !important;
        max-width: 100vw !important;
        margin: 0 !important;
        padding: 0 !important;
        position: relative !important;
        min-height: 100vh !important;
    }

    /* Force all direct children of body to respect width */
    body > * {
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    /* Prevent any element from causing horizontal scroll */
    div, section, article, main, header, footer, nav, aside {
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    /* Specific fixes for common overflow elements */
    img, video, iframe, canvas, svg {
        max-width: 100% !important;
        width: auto !important;
        height: auto !important;
    }

    /* Fix for any floating or positioned elements */
    .hero-visual, .content-visual, .service-hero-content, .hero-content {
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    /* Prevent transforms from causing overflow - but allow mobile menu */
    *:not(.mobile-menu):not(.mobile-menu-toggle):not(.mobile-menu *) {
        transform: none !important;
        left: auto !important;
        right: auto !important;
        position: relative !important;
    }

    /* Exception for necessary positioning and mobile menu */
    .mobile-menu-toggle, .header, .nav-menu, .mobile-menu {
        position: relative !important;
    }

    /* Allow mobile menu to use transforms */
    .mobile-menu {
        position: fixed !important;
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease-in-out !important;
    }

    .mobile-menu.active {
        transform: translateX(0) !important;
    }

    .container {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 1rem !important;
        margin: 0 auto !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
    }

    /* Fix for main wrapper */
    .main-wrapper, .page-wrapper, .content-wrapper {
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Fix for any grid or flex containers */
    .row, .grid, .flex-container {
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
        margin: 0 !important;
    }

    /* Amazon Services Pages Mobile Responsive Fixes */
    .service-hero,
    .hero.service-hero,
    .hero {
        padding: 2rem 0 !important;
        text-align: center !important;
        width: 100% !important;
        overflow-x: hidden !important;
    }

    .service-hero-content,
    .hero-content {
        display: flex !important;
        flex-direction: column !important;
        gap: 2rem !important;
        width: 100% !important;
        max-width: 100% !important;
        align-items: center !important;
        justify-content: center !important;
        overflow-x: hidden !important;
        margin: 0 !important;
        padding: 0 1rem !important;
        box-sizing: border-box !important;
    }

    /* Hero Image First - Bigger and Centered */
    .hero-visual {
        order: 1 !important;
        margin: 0 auto 2rem auto !important;
        text-align: center !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
    }

    /* Hero Content Below Image */
    .hero-text {
        order: 2 !important;
        text-align: center !important;
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 1rem !important;
    }

    /* Make hero images bigger and centered */
    .hero-image,
    .service-hero .hero-image,
    .hero.service-hero .hero-image {
        width: 90% !important;
        max-width: 450px !important;
        height: auto !important;
        border-radius: 16px !important;
        box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
        object-fit: cover !important;
        display: block !important;
        margin: 0 auto !important;
        transition: all 0.3s ease !important;
    }

    /* Hover effect for hero images on mobile */
    .hero-image:hover,
    .service-hero .hero-image:hover,
    .hero.service-hero .hero-image:hover {
        transform: scale(1.02) !important;
        box-shadow: 0 16px 50px rgba(0,0,0,0.2) !important;
    }

    .hero-title {
        font-size: 1.8rem !important;
        line-height: 1.3 !important;
        margin-bottom: 1rem !important;
        padding: 0 !important;
        text-align: center !important;
    }

    .hero-description {
        font-size: 1rem !important;
        line-height: 1.5 !important;
        margin-bottom: 1.5rem !important;
        padding: 0 !important;
        text-align: center !important;
    }

    .hero-actions {
        display: flex !important;
        flex-direction: column !important;
        gap: 1rem !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        padding: 0 !important;
    }

    .hero-actions .btn {
        width: 90% !important;
        max-width: 280px !important;
        justify-content: center !important;
        text-align: center !important;
    }

    /* Content sections mobile fixes */
    .content-grid {
        display: flex !important;
        flex-direction: column !important;
        gap: 2rem !important;
        width: 100% !important;
        max-width: 100% !important;
        align-items: center !important;
    }

    .content-text,
    .content-visual {
        order: unset !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    .content-visual {
        text-align: center !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        width: 100% !important;
        padding: 0 !important;
    }

    .content-image {
        width: 90% !important;
        max-width: 350px !important;
        height: auto !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 30px rgba(0,0,0,0.1) !important;
        display: block !important;
        margin: 0 auto !important;
        object-fit: cover !important;
        transition: all 0.3s ease !important;
    }

    /* Hover effect for content images on mobile */
    .content-image:hover {
        transform: scale(1.02) !important;
        box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
    }

    /* Benefits grid mobile */
    .benefits-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }

    .benefit-card {
        text-align: center !important;
        padding: 1.5rem !important;
    }

    /* Process steps mobile */
    .process-steps {
        flex-direction: column !important;
        gap: 1.5rem !important;
    }

    .process-step {
        text-align: center !important;
        padding: 1.5rem !important;
    }

    /* Section headers mobile */
    .section-header {
        text-align: center !important;
        margin-bottom: 2rem !important;
    }

    .section-title {
        font-size: 1.8rem !important;
        line-height: 1.3 !important;
    }

    .section-description {
        font-size: 1rem !important;
    }

    /* Ensure all sections and containers respect mobile viewport */
    .section, .hero, .hero-section, .services-section, .about-section, .contact-section {
        max-width: 100vw !important;
        overflow-x: hidden !important;
        box-sizing: border-box !important;
    }

    /* FORCE MOBILE NAVIGATION TO SHOW */
    .nav-menu,
    .user-menu,
    .auth-actions {
        display: none !important;
    }

    .mobile-menu-toggle {
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        width: 44px !important;
        height: 44px !important;
        background: transparent !important;
        border: none !important;
        cursor: pointer !important;
        padding: 10px !important;
        z-index: 10001 !important;
        position: relative !important;
        pointer-events: auto !important;
        touch-action: manipulation !important;
        margin-left: auto !important;
    }

    .mobile-menu-toggle span {
        display: block !important;
        width: 25px !important;
        height: 3px !important;
        background: var(--text-primary) !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 2px !important;
    }

    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px) !important;
    }

    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0 !important;
    }

    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px) !important;
    }

    .mobile-menu {
        display: block !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100vh !important;
        background: var(--bg-card) !important;
        z-index: 10000 !important;
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease-in-out !important;
        overflow-y: auto !important;
        visibility: hidden !important;
        opacity: 0 !important;
    }

    .mobile-menu.active {
        transform: translateX(0) !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* COMPLETELY REMOVE FLOATING CARDS ON MOBILE */
    .hero-visual,
    .floating-card,
    .hero-main-visual {
        display: none !important;
    }

    /* HERO SECTION MOBILE LAYOUT */
    .hero {
        padding: 2rem 0 !important;
        overflow: hidden !important;
        position: relative !important;
    }

    .hero-content {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        text-align: center !important;
        gap: 2rem !important;
        position: relative !important;
        z-index: 2 !important;
    }

    .hero-text {
        order: 1 !important;
        width: 100% !important;
        max-width: none !important;
    }

    .hero-image {
        order: 2 !important;
        width: 100% !important;
        max-width: 300px !important;
        margin: 0 auto !important;
    }

    /* STATS SECTION - CENTER ALIGNED IN ONE ROW */
    .hero-stats {
        display: flex !important;
        flex-direction: row !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 1.5rem !important;
        margin-top: 2rem !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100% !important;
        order: 3 !important;
    }

    .hero-stats .stat-item {
        flex: 1 !important;
        min-width: 80px !important;
        max-width: 120px !important;
        text-align: center !important;
    }

    .hero-stats .stat-number {
        font-size: 1.5rem !important;
        font-weight: 700 !important;
        color: var(--purple-primary) !important;
        margin-bottom: 0.25rem !important;
        line-height: 1.2 !important;
    }

    .hero-stats .stat-label {
        font-size: 0.75rem !important;
        color: var(--text-secondary) !important;
        font-weight: 500 !important;
        line-height: 1.2 !important;
    }

    .hero-content {
        display: flex !important;
        flex-direction: column !important;
        gap: 2rem !important;
        text-align: center !important;
        grid-template-columns: none !important;
    }

    .hero-text {
        order: 1 !important;
        width: 100% !important;
    }

    .hero-image {
        order: 2 !important;
        width: 100% !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        min-height: auto !important;
        background: transparent !important;
        border-radius: 0 !important;
        margin-top: 0 !important;
    }

    /* Remove the purple Digital Solutions box completely */
    .hero-image::after {
        display: none !important;
        content: none !important;
    }

    /* TYPOGRAPHY MOBILE */
    .hero-title {
        font-size: 2.2rem !important;
        line-height: 1.2 !important;
        margin-bottom: 1rem !important;
    }

    .hero-description {
        font-size: 1rem !important;
        line-height: 1.6 !important;
        margin-bottom: 1.5rem !important;
    }

    .section-title {
        font-size: 1.8rem !important;
        line-height: 1.3 !important;
    }

    /* BUTTONS MOBILE */
    .hero-buttons {
        display: flex !important;
        flex-direction: column !important;
        gap: 1rem !important;
        align-items: center !important;
        width: 100% !important;
        margin-top: 1.5rem !important;
    }

    .hero-buttons .btn {
        width: 100% !important;
        max-width: 280px !important;
        padding: 1rem 1.5rem !important;
        font-size: 1rem !important;
        justify-content: center !important;
    }

    /* STATS MOBILE - Removed conflicting styles */

    /* GENERAL GRID LAYOUTS */
    .services-grid,
    .testimonials-grid,
    .mvv-grid,
    .team-grid,
    .features-grid,
    .about-features {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }

    /* Content features - keep in one row */
    .content-features {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 0.25rem !important;
    }

    .content-features .feature-item {
        flex-direction: column !important;
        text-align: center !important;
        gap: 0.15rem !important;
        font-size: 0.7rem !important;
        min-width: 0 !important;
        padding: 0.25rem !important;
    }

    .content-features .feature-item i {
        font-size: 0.9rem !important;
    }

    .content-features .feature-item span {
        line-height: 1.1 !important;
        word-break: break-word !important;
        hyphens: auto !important;
        overflow-wrap: break-word !important;
        white-space: normal !important;
    }

    .about-content,
    .content-grid {
        grid-template-columns: 1fr !important;
        gap: 2rem !important;
        text-align: center !important;
    }

    /* CARDS AND COMPONENTS */
    .service-card,
    .feature-card,
    .testimonial-card {
        padding: 1.5rem !important;
        margin-bottom: 1rem !important;
        border-radius: 12px !important;
        box-shadow: var(--shadow-sm) !important;
    }

    .service-card h3 {
        font-size: 1.2rem !important;
        margin-bottom: 0.75rem !important;
    }

    .service-card p {
        font-size: 0.95rem !important;
        line-height: 1.6 !important;
    }

    /* FORMS */
    .form-row {
        flex-direction: column !important;
        gap: 1rem !important;
    }

    .form-group {
        margin-bottom: 1rem !important;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px !important; /* Prevents zoom on iOS */
        padding: 1rem !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    /* CONTACT PAGE */
    .contact-grid {
        grid-template-columns: 1fr !important;
        gap: 2rem !important;
    }

    .contact-cards {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }

    .contact-card {
        padding: 1.5rem !important;
        text-align: center !important;
    }

    /* TEAM SLIDER - MOBILE: ONE MEMBER AT A TIME */
    .team-slider-container {
        max-width: 100% !important;
        padding: 0 1rem !important;
        overflow: hidden !important;
    }

    .team-slide {
        grid-template-columns: 1fr !important;
        gap: 2rem !important;
        padding: 0 1rem !important;
    }

    /* Hide all members except the currently active one */
    .team-member {
        display: none !important;
        width: 100% !important;
        max-width: 320px !important;
        margin: 0 auto !important;
        padding: 2rem 1.5rem !important;
        text-align: center !important;
    }

    /* Show only the active member */
    .team-member.mobile-active {
        display: block !important;
    }

    .slider-nav {
        padding: 0 0.5rem !important;
        margin-top: 2rem !important;
    }

    .slider-btn {
        width: 44px !important;
        height: 44px !important;
        background: var(--purple-primary) !important;
        color: white !important;
        border: none !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .slider-btn:hover {
        background: var(--purple-secondary) !important;
        transform: scale(1.1) !important;
    }

    .slider-btn i {
        font-size: 1.2rem !important;
    }

    .slider-dots {
        margin-top: 1.5rem !important;
        display: flex !important;
        justify-content: center !important;
        gap: 0.5rem !important;
    }

    .dot {
        width: 12px !important;
        height: 12px !important;
        border-radius: 50% !important;
        background: var(--border-primary) !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .dot.active {
        background: var(--purple-primary) !important;
        transform: scale(1.2) !important;
    }

    /* Mobile team member content styling */
    .team-member .member-avatar {
        width: 80px !important;
        height: 80px !important;
        margin: 0 auto 1.5rem auto !important;
        background: var(--purple-primary) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .team-member .member-avatar i {
        font-size: 2rem !important;
        color: white !important;
    }

    .team-member .member-info h3 {
        font-size: 1.5rem !important;
        margin-bottom: 0.5rem !important;
        color: var(--text-primary) !important;
    }

    .team-member .member-role {
        font-size: 1rem !important;
        color: var(--purple-primary) !important;
        font-weight: 600 !important;
        margin-bottom: 1rem !important;
        display: block !important;
    }

    .team-member .member-bio {
        font-size: 0.9rem !important;
        line-height: 1.6 !important;
        color: var(--text-secondary) !important;
        margin-bottom: 1.5rem !important;
    }

    .team-member .member-social {
        display: flex !important;
        justify-content: center !important;
        gap: 1rem !important;
    }

    .team-member .member-social a {
        width: 40px !important;
        height: 40px !important;
        background: var(--bg-secondary) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: var(--text-secondary) !important;
        transition: all 0.3s ease !important;
    }

    .team-member .member-social a:hover {
        background: var(--purple-primary) !important;
        color: white !important;
        transform: translateY(-2px) !important;
    }

    /* SECTIONS */
    section {
        padding: 3rem 0 !important;
    }

    .content-grid.content-centered {
        padding: 1rem !important;
    }

    .about-content {
        min-height: auto !important;
        padding: 2rem 0 !important;
    }

    /* BUTTONS */
    .btn {
        padding: 1rem 1.5rem !important;
        font-size: 1rem !important;
        min-height: 44px !important;
        justify-content: center !important;
    }

    .cta-buttons {
        flex-direction: column !important;
        gap: 1rem !important;
        align-items: center !important;
    }

    .cta-buttons .btn {
        width: 100% !important;
        max-width: 280px !important;
    }
}

/* Additional mobile fixes for smaller screens - Amazon Services Hero */
@media (max-width: 480px) {
    /* Amazon Services Hero Mobile */
    .service-hero,
    .hero.service-hero,
    .hero {
        padding: 1.5rem 0 !important;
    }

    .service-hero-content,
    .hero-content {
        gap: 1.5rem !important;
    }

    .hero-image,
    .service-hero .hero-image,
    .hero.service-hero .hero-image {
        width: 85% !important;
        max-width: 380px !important;
        border-radius: 14px !important;
        margin: 0 auto !important;
    }

    .hero-title {
        font-size: 1.6rem !important;
        line-height: 1.2 !important;
    }

    .hero-description {
        font-size: 0.95rem !important;
        line-height: 1.5 !important;
    }

    .hero-actions {
        flex-direction: column !important;
        gap: 1rem !important;
        align-items: center !important;
    }

    .hero-actions .btn {
        width: 100% !important;
        max-width: 280px !important;
        justify-content: center !important;
    }
}

/* Medium Mobile Screens (481px to 767px) - Enhanced Amazon Services */
@media (max-width: 767px) and (min-width: 481px) {
    .hero-image,
    .service-hero .hero-image,
    .hero.service-hero .hero-image {
        width: 85% !important;
        max-width: 420px !important;
        border-radius: 16px !important;
        box-shadow: 0 12px 40px rgba(0,0,0,0.15) !important;
        margin: 0 auto !important;
    }

    .content-image {
        width: 85% !important;
        max-width: 380px !important;
        margin: 0 auto !important;
        display: block !important;
        border-radius: 14px !important;
        box-shadow: 0 10px 35px rgba(0,0,0,0.12) !important;
    }

    .hero-visual,
    .content-visual {
        width: 100% !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        padding: 0 !important;
    }
}

/* Tablet responsive (769px to 1024px) */
@media (max-width: 1024px) and (min-width: 769px) {
    .team-slide {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }

    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }

    .hero-title {
        font-size: 3rem;
    }

    .section-title {
        font-size: 2.25rem;
    }

    /* Content features on tablets - 2 columns */
    .content-features {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
}

/* Mobile responsive styles are consolidated above */

/* ===== LANDSCAPE ORIENTATION IMPROVEMENTS ===== */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section,
    .service-hero {
        padding: 1.5rem 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    /* Amazon Services in landscape - keep single column to prevent overflow */
    .service-hero-content,
    .hero-content {
        display: flex !important;
        flex-direction: column !important;
        gap: 1.5rem !important;
        align-items: center !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    .hero-visual {
        order: 1 !important;
        width: 100% !important;
    }

    .hero-text {
        order: 2 !important;
        text-align: center !important;
        width: 100% !important;
    }

    .hero-image,
    .service-hero .hero-image,
    .hero.service-hero .hero-image {
        width: 80% !important;
        max-width: 350px !important;
        margin: 0 auto !important;
    }

    .mobile-menu-content {
        padding: 1rem;
        columns: 2;
        column-gap: 2rem;
    }

    .mobile-menu-actions {
        column-span: all;
        margin-top: 1rem;
        padding-top: 1rem;
    }
}

/* ===== HIGH DPI DISPLAYS ===== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo-img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== DARK MODE SUPPORT (if user prefers) ===== */
@media (prefers-color-scheme: dark) {
    /* This can be expanded later if dark mode is desired */
}

/* ===== FINAL MOBILE OPTIMIZATIONS ===== */
@media (max-width: 768px) {
    /* Header fixes for mobile */
    .header {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 !important;
        overflow-x: hidden !important;
    }

    .header .container {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 1rem !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    /* Force mobile menu toggle to be visible */
    .mobile-menu-toggle {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 1000 !important;
    }

    /* Hide desktop navigation on mobile */
    .nav-menu,
    .user-menu,
    .auth-actions {
        display: none !important;
    }

    /* Ensure all interactive elements are touch-friendly */
    a, button, input, select, textarea {
        min-height: 44px;
        min-width: 44px;
    }

    /* COMPLETE FLOATING CARDS MOBILE FIX */
    .floating-card,
    .floating-card:hover,
    .floating-card:focus,
    .floating-card:active {
        position: relative !important;
        transform: none !important;
        animation: none !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: none !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        box-shadow: none !important;
        border: none !important;
        background: transparent !important;
        background-color: transparent !important;
        background-image: none !important;
        border-radius: 8px !important;
        padding: 0.75rem 1rem !important;
        min-height: 50px !important;
        z-index: 1 !important;
        filter: none !important;
    }

    /* Ensure hero visual container is properly sized */
    .hero-visual {
        height: auto !important;
        width: 100% !important;
        margin: 0 !important;
        position: relative !important;
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 0.75rem !important;
        padding: 1rem !important;
        overflow: visible !important;
    }

    /* Hide complex visual elements on mobile */
    .hero-main-visual,
    .visual-content,
    .visual-header {
        display: none !important;
    }

    /* Mobile-specific hero improvements */
    .hero {
        min-height: auto;
        padding: 2rem 0;
    }

    .hero-content {
        text-align: center;
    }

    .hero-buttons {
        flex-direction: column !important;
        gap: 1rem !important;
        align-items: center !important;
        width: 100% !important;
        margin-top: 1.5rem !important;
        position: relative !important;
        z-index: 4 !important;
    }

    .hero-buttons .btn {
        width: 100% !important;
        max-width: 300px !important;
        position: relative !important;
        z-index: 5 !important;
    }

    /* Mobile navigation final touches */
    .mobile-menu {
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
    }

    /* Ensure proper spacing on all sections */
    section {
        padding: 2rem 0 !important;
        position: relative !important;
        z-index: 1 !important;
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    /* Content sections specific fixes */
    .content-section {
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    .section-header {
        text-align: center !important;
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 1rem !important;
    }

    .section-title {
        font-size: 1.8rem !important;
        line-height: 1.3 !important;
        margin-bottom: 1rem !important;
        text-align: center !important;
    }

    .section-description {
        font-size: 1rem !important;
        line-height: 1.5 !important;
        text-align: center !important;
        max-width: 90% !important;
        margin: 0 auto !important;
    }

    .section-padding {
        padding: 3rem 0;
    }

    /* Mobile-optimized cards and components */
    .card, .service-card, .feature-card, .testimonial-card {
        margin-bottom: 1rem;
        border-radius: 12px;
        position: relative;
        z-index: 2;
    }

    /* Ensure text is readable on mobile */
    body {
        font-size: 16px;
        line-height: 1.6;
        overflow-x: hidden;
    }

    /* Prevent any absolute positioning issues */
    .hero-content > * {
        position: relative;
        z-index: 2;
    }

    /* Ensure hero stats don't overlap */
    .hero-stats {
        position: relative !important;
        z-index: 3 !important;
        background: transparent !important;
        margin-top: 2rem !important;
    }

    /* Mobile-specific utility classes */
    .mobile-hidden {
        display: none !important;
    }

    .mobile-center {
        text-align: center !important;
    }

    .mobile-full-width {
        width: 100% !important;
    }

    /* Fix any remaining overlapping issues */
    * {
        box-sizing: border-box;
    }

    .container {
        position: relative;
        z-index: 1;
    }
}

/* ===== COMPREHENSIVE MOBILE FIXES ===== */
@media screen and (max-width: 768px) {
    /* CRITICAL: Prevent all overlapping issues */
    .hero {
        position: relative !important;
        overflow: hidden !important;
        z-index: 1 !important;
    }

    .hero-content {
        position: relative !important;
        z-index: 2 !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 2rem !important;
        align-items: center !important;
        text-align: center !important;
    }

    .hero-text {
        order: 1 !important;
        position: relative !important;
        z-index: 3 !important;
        width: 100% !important;
    }

    .hero-image {
        order: 2 !important;
        position: relative !important;
        z-index: 2 !important;
        margin-top: 0 !important;
        width: 100% !important;
        max-width: 300px !important;
    }

    /* Ensure mobile menu toggle is always visible and functional */
    .mobile-menu-toggle {
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        width: 44px !important;
        height: 44px !important;
        background: transparent !important;
        border: none !important;
        cursor: pointer !important;
        padding: 8px !important;
        z-index: 10001 !important;
        position: relative !important;
        gap: 4px !important;
        outline: none !important;
        -webkit-tap-highlight-color: transparent !important;
        touch-action: manipulation !important;
        user-select: none !important;
    }

    .mobile-menu-toggle:hover {
        background-color: rgba(139, 92, 246, 0.1) !important;
        border-radius: 4px !important;
    }

    .mobile-menu-toggle:focus {
        outline: 2px solid var(--purple-primary) !important;
        outline-offset: 2px !important;
    }

    .mobile-menu-toggle span {
        display: block !important;
        width: 25px !important;
        height: 3px !important;
        background-color: var(--text-primary) !important;
        transition: all 0.3s ease !important;
        border-radius: 2px !important;
        transform-origin: center !important;
        pointer-events: none !important;
    }

    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px) !important;
    }

    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0 !important;
    }

    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px) !important;
    }

    /* Mobile menu container - FIXED */
    .mobile-menu {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100vh !important;
        background-color: var(--bg-card) !important;
        z-index: 10000 !important;
        transform: translateX(-100%) !important;
        transition: transform 0.3s ease-in-out !important;
        overflow-y: auto !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .mobile-menu.active {
        transform: translateX(0) !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .mobile-menu-content {
        padding: 2rem !important;
        padding-top: 4rem !important;
        width: 100% !important;
        height: 100% !important;
        box-sizing: border-box !important;
    }

    .mobile-menu-close {
        position: absolute !important;
        top: 1rem !important;
        right: 1rem !important;
        background: none !important;
        border: none !important;
        font-size: 1.5rem !important;
        color: var(--text-primary) !important;
        cursor: pointer !important;
        z-index: 10001 !important;
        width: 44px !important;
        height: 44px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .mobile-nav-link {
        display: block !important;
        padding: 1rem 0 !important;
        color: var(--text-primary) !important;
        text-decoration: none !important;
        font-weight: 500 !important;
        border-bottom: 1px solid var(--border-primary) !important;
        transition: var(--transition-normal) !important;
    }

    .mobile-nav-link:hover {
        color: var(--purple-primary) !important;
    }

    /* Hide desktop navigation */
    .nav-menu,
    .user-menu,
    .auth-actions {
        display: none !important;
    }
}

/* ===== VERY SMALL SCREENS (320px and below) ===== */
@media (max-width: 320px) {
    .container {
        padding: 0 0.75rem;
    }

    .hero-title {
        font-size: 1.75rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .btn {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }

    .service-card,
    .feature-card {
        padding: 1rem;
    }

    /* Amazon Services Images for very small screens */
    .hero-image,
    .service-hero .hero-image,
    .hero.service-hero .hero-image {
        max-width: 320px !important;
        transform: scale(1.0) !important;
        border-radius: 12px !important;
    }

    .content-image {
        max-width: 280px !important;
        width: 100% !important;
        margin: 0 auto !important;
        display: block !important;
        border-radius: 12px !important;
    }

    /* Tech grid for very small screens */
    .tech-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.3rem;
    }

    .tech-item {
        padding: 0.3rem;
        min-height: 50px;
    }

    .tech-item i {
        font-size: 0.9rem;
    }

    .tech-item span {
        font-size: 0.6rem;
        line-height: 1;
    }

    /* Portfolio stats for very small screens */
    .portfolio-stats .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 0.25rem;
    }

    .portfolio-stats .stat-item {
        padding: 0.75rem 0.15rem;
    }

    .portfolio-stats .stat-number {
        font-size: 1.25rem;
    }

    .portfolio-stats .stat-label {
        font-size: 0.6rem;
        line-height: 1.1;
    }

    /* Content features for very small screens - use 2 columns */
    .content-features {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 0.5rem !important;
    }

    .content-features .feature-item {
        flex-direction: row !important;
        text-align: left !important;
        gap: 0.4rem !important;
        font-size: 0.75rem !important;
        padding: 0.4rem !important;
    }

    .content-features .feature-item i {
        font-size: 0.9rem !important;
        flex-shrink: 0 !important;
    }

    .content-features .feature-item span {
        line-height: 1.2 !important;
        word-break: break-word !important;
    }
}

/* MOBILE STATS OVERRIDE - ENSURE PROPER DISPLAY */
@media (max-width: 768px) {
    /* ABOUT SECTION MOBILE - CENTER EVERYTHING */
    .about-content {
        flex-direction: column !important;
        gap: 1rem !important;
        text-align: center !important;
        align-items: center !important;
        padding: 1.5rem 0 !important;
    }

    .about-text {
        width: 100% !important;
        max-width: none !important;
        text-align: center !important;
    }

    .about-image {
        width: 100% !important;
        max-width: 400px !important;
        margin: 0 auto !important;
    }

    .about-features {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
        gap: 0.5rem !important;
        flex-wrap: nowrap !important;
        margin-bottom: 1rem !important;
    }

    .feature-item {
        flex: 1 !important;
        flex-direction: column !important;
        text-align: center !important;
        gap: 0.25rem !important;
        align-items: center !important;
        min-width: 0 !important;
        padding: 0.5rem !important;
    }

    .feature-item i {
        font-size: 1rem !important;
        margin-bottom: 0.25rem !important;
    }

    .feature-item span {
        font-size: 0.75rem !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }

    /* GENERAL GRID LAYOUTS */
    .services-grid,
    .testimonials-grid,
    .mvv-grid,
    .team-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }

    .section-header {
        text-align: center !important;
        margin-bottom: 2rem !important;
    }

    .section-title {
        font-size: 2rem !important;
        margin-bottom: 1rem !important;
    }

    .section-description {
        font-size: 1rem !important;
        line-height: 1.6 !important;
    }
}

/* Very small screens - make stats even more compact */
@media (max-width: 480px) {
    .hero-stats {
        gap: 0.5rem;
    }

    .hero-stats .stat-item {
        min-width: 70px;
    }

    .hero-stats .stat-number {
        font-size: 1.25rem;
    }

    .hero-stats .stat-label {
        font-size: 0.7rem;
    }

    .stats-card {
        flex-direction: row !important;
        gap: 1rem !important;
        justify-content: space-between !important;
        align-items: center !important;
        flex-wrap: nowrap !important;
        padding: 1.5rem !important;
    }

    .stats-card .stat-item {
        flex: 1 !important;
        text-align: center !important;
        min-width: 0 !important;
    }

    .stats-card .stat-number {
        font-size: 1.5rem !important;
        margin-bottom: 0.25rem !important;
    }

    .stats-card .stat-label {
        font-size: 0.75rem !important;
        white-space: nowrap !important;
    }

    .nav-menu {
        display: none;
    }

    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
    }

    .notification {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        min-width: auto;
    }
}

/* Contact Page Styles */
.contact-section {
    padding: var(--section-padding);
    background: var(--bg-primary);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: flex-start;
}

.contact-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 2rem;
}

.contact-cards {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.contact-card {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    transition: var(--transition-normal);
}

.contact-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--purple-primary);
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--purple-light);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--purple-primary);
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.contact-details p {
    color: var(--text-secondary);
    line-height: 1.5;
    margin: 0;
}

.contact-details a {
    color: var(--purple-primary);
    text-decoration: none;
    transition: var(--transition-normal);
}

.contact-details a:hover {
    color: var(--purple-secondary);
}

.social-links h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 45px;
    height: 45px;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-normal);
}

.social-link:hover {
    background: var(--purple-primary);
    color: var(--text-white);
    border-color: var(--purple-primary);
    transform: translateY(-2px);
}

/* Contact Form */
.contact-form-container {
    position: sticky;
    top: 2rem;
}

.form-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-md);
}

.form-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem;
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition-normal);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--purple-primary);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.btn-full {
    width: 100%;
    justify-content: center;
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.alert-success {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.alert-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

/* Map Section */
.map-section {
    padding: 2rem 0;
    background: var(--bg-secondary);
}

.map-container {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 4rem 2rem;
    text-align: center;
}

.map-placeholder {
    color: var(--text-muted);
}

.map-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
    color: var(--purple-primary);
}

.map-placeholder h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.map-placeholder p {
    margin-bottom: 2rem;
    color: var(--text-secondary);
}

/* Pricing Tables Styles */
.pricing-section {
    padding: 0px 0;
    background: var(--bg-primary);
}

.pricing-table-container {
    margin-bottom: 3rem;
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
}

.pricing-table {
    width: 100%;
    background: var(--bg-card);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--border-primary);
}

.pricing-header {
    display: grid;
    grid-template-columns: 200px repeat(5, 1fr);
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
    color: var(--text-white);
    font-weight: 600;
}

.pricing-header .pricing-cell {
    padding: 1rem;
    text-align: center;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 0.9rem;
}

.pricing-header .pricing-cell:first-child {
    text-align: left;
    background: rgba(0, 0, 0, 0.1);
}

.pricing-header .pricing-cell:last-child {
    border-right: none;
}

.pricing-row {
    display: grid;
    grid-template-columns: 200px repeat(5, 1fr);
    border-bottom: 1px solid var(--border-primary);
}

.pricing-row:nth-child(even) {
    background: var(--bg-secondary);
}

.pricing-row:last-child {
    border-bottom: none;
}

.pricing-cell {
    padding: 1rem;
    text-align: center;
    border-right: 1px solid var(--border-primary);
    font-size: 0.9rem;
    line-height: 1.4;
    color: var(--text-primary);
}

.pricing-cell:last-child {
    border-right: none;
}

.feature-cell {
    background: var(--purple-primary);
    color: var(--text-white) !important;
    font-weight: 600;
    text-align: left !important;
}

.pricing-cell.available {
    color: #16a34a;
    font-weight: 600;
}

.pricing-cell.unavailable {
    color: #dc2626;
    font-weight: 600;
}

.action-row {
    background: var(--bg-tertiary) !important;
    font-weight: 600;
    color: var(--purple-primary);
}

/* Second pricing table with 4 columns */
.pricing-table-container:last-child .pricing-header,
.pricing-table-container:last-child .pricing-row {
    grid-template-columns: 200px repeat(3, 1fr);
}

.pricing-table-container:last-child .pricing-table {
    width: 100%;
}

/* Mobile styles for second table */
@media (max-width: 768px) {
    .pricing-table-container:last-child .pricing-table {
        min-width: 900px;
    }
}

/* Important Note Styles */
.important-note {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 1px solid #ffc107;
    border-radius: 12px;
    padding: 1.5rem;
    margin-top: 2rem;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.note-icon {
    color: #856404;
    font-size: 1.5rem;
    margin-top: 0.25rem;
    flex-shrink: 0;
}

.note-content h4 {
    color: #856404;
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.note-content p {
    color: #856404;
    margin: 0;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Responsive Design for Pricing Tables */
@media (max-width: 768px) {
    .pricing-section {
        padding: 60px 0;
    }

    .pricing-table-container {
        margin: 0 -1rem 3rem -1rem;
        border-radius: 0;
        overflow-x: auto;
    }

    .pricing-table {
        min-width: 800px;
    }

    .pricing-cell {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .pricing-header .pricing-cell {
        padding: 0.75rem 0.5rem;
        font-size: 0.8rem;
    }

    .important-note {
        margin: 2rem -1rem 0 -1rem;
        border-radius: 0;
        padding: 1rem;
    }
}

/* CTA Section Styles */
.cta-contact {
    margin: 1.5rem 0;
}

.cta-email {
    font-size: 1.1rem;
    margin-bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.email-link {
    color: var(--yellow-primary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.email-link:hover {
    color: #fff;
    text-decoration: underline;
}

.btn-white {
    background: white;
    color: var(--purple-primary);
    border: 2px solid white;
}

.btn-white:hover {
    background: transparent;
    color: white;
    border-color: white;
}

.btn-outline-white {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-outline-white:hover {
    background: white;
    color: var(--purple-primary);
}

/* Purple CTA Section Button Overrides */
.bg-purple .btn-primary {
    background: white;
    color: var(--purple-primary);
    border: 2px solid white;
}

.bg-purple .btn-primary:hover {
    background: transparent;
  color: var(--purple-primary);
    border-color: white;
}

.bg-purple .btn-outline {
    background: transparent;
    color: var(--purple-primary);
    border: 2px solid white;
}

.bg-purple .btn-outline:hover {
    background: white;
    color: var(--purple-primary);
    color: var(--purple-primary);
}

@media (max-width: 768px) {
    .cta-email {
        font-size: 1rem;
        flex-direction: column;
        gap: 0.25rem;
    }

    .content-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .content-actions .btn {
        width: 100%;
        text-align: center;
    }

    .note-icon {
        font-size: 1.25rem;
    }

    .note-content h4 {
        font-size: 1rem;
    }

    .note-content p {
        font-size: 0.9rem;
    }
}

/* Portfolio Page Styles */
.portfolio-stats {
    padding: 3rem 0;
    background: var(--bg-secondary);
}

.portfolio-stats .stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    text-align: center;
}

.portfolio-stats .stat-item {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    padding: 2rem 1rem;
    transition: var(--transition-normal);
}

.portfolio-stats .stat-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.portfolio-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--purple-primary);
    margin-bottom: 0.5rem;
}

.portfolio-stats .stat-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.portfolio-filters {
    padding: 2rem 0;
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
}

.filter-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 25px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-normal);
    font-weight: 500;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--purple-primary);
    color: var(--text-white);
    border-color: var(--purple-primary);
    transform: translateY(-2px);
}

.portfolio-section {
    padding: var(--section-padding);
    background: var(--bg-primary);
}

.portfolio-grid {
    columns: 4;
    column-gap: 1.5rem;
    padding: 2rem 0;
}

@media (max-width: 1200px) {
    .portfolio-grid {
        columns: 3;
        column-gap: 1.25rem;
    }
}

@media (max-width: 768px) {
    .portfolio-grid {
        columns: 3;
        column-gap: 0.75rem;
    }
}

@media (max-width: 480px) {
    .portfolio-grid {
        columns: 2;
        column-gap: 0.5rem;
    }
}

.portfolio-item {
    background: transparent;
    border: none;
    border-radius: var(--border-radius-lg);
    overflow: visible;
    transition: var(--transition-normal);
    display: inline-block;
    cursor: pointer;
    break-inside: avoid;
    margin-bottom: 1.5rem;
    width: 100%;
    position: relative;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.portfolio-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.portfolio-item:hover .portfolio-main-image {
    transform: scale(1.02);
}

.portfolio-image {
    overflow: hidden;
    border-radius: 16px;
    position: relative;
    border-radius: var(--border-radius-lg);
}

.portfolio-main-image {
    width: 100%;
    height: auto;
    object-fit: contain;
    transition: var(--transition-normal);
    border-radius: var(--border-radius-lg);
    display: block;
    max-height: none;
    min-height: auto;
    background: transparent;
}

.portfolio-item:hover .portfolio-main-image {
    transform: scale(1.02);
}

.portfolio-image {
    position: relative;
    width: 100%;
    border-radius: var(--border-radius-lg);
    overflow: visible;
}

.portfolio-image {
    height: auto;
    min-height: auto;
    background: transparent;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-lg);
}

/* Override for video items to show full content */
.video-item .portfolio-image {
    height: auto;
    min-height: auto;
    overflow: visible;
    background: transparent;
    display: block;
}

.portfolio-placeholder {
    color: rgba(255, 255, 255, 0.9);
    font-size: 4rem;
    z-index: 2;
    position: relative;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: var(--transition-normal);
    padding: 2rem;
    text-align: center;
    color: var(--text-white);
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.overlay-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-white);
}

.overlay-content p {
    margin-bottom: 2rem;
    opacity: 0.9;
}

.portfolio-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.portfolio-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
}

.portfolio-btn:hover {
    background: var(--purple-primary);
    border-color: var(--purple-primary);
    transform: scale(1.1);
}

.portfolio-btn i {
    font-size: 1rem;
}

/* Video Portfolio Styles */
.portfolio-main-video {
    width: 100%;
    height: 200px;
    display: block;
    object-fit: cover;
    transition: var(--transition-normal);
    border-radius: var(--border-radius-lg);
    background: transparent;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    pointer-events: none;
}

.play-icon {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--purple-primary);
    font-size: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.video-item:hover .play-icon {
    transform: scale(1.1);
    background: var(--purple-primary);
    color: white;
}

.video-item:hover .video-overlay {
    background: rgba(0, 0, 0, 0.1);
}

.video-item:hover .portfolio-main-video {
    transform: scale(1.05);
}

.video-item .portfolio-image:hover .portfolio-main-video {
    transform: scale(1.05);
}

/* Make video items show consistent sizing */
.video-item .portfolio-image {
    height: 200px;
    overflow: hidden;
    display: block;
}

.video-item .portfolio-main-video {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
}

.portfolio-content {
    padding: 2rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.portfolio-title {
    font-size: 1.4rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.portfolio-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex: 1;
}

.portfolio-tags {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.portfolio-tags span {
    background: var(--purple-light);
    color: var(--purple-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.portfolio-client {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-muted);
    font-size: 0.9rem;
}

.portfolio-client i {
    color: var(--orange-primary);
}

/* Responsive Design for New Pages */
@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .contact-form-container {
        position: static;
    }

    /* Portfolio Mobile Fixes */
    .portfolio-stats .stats-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem !important;
    }

    .portfolio-stats .stat-item {
        padding: 1.5rem 1rem !important;
        text-align: center !important;
    }

    .portfolio-stats .stat-number {
        font-size: 2rem !important;
    }

    .portfolio-stats .stat-label {
        font-size: 0.9rem !important;
        line-height: 1.3 !important;
    }

    /* Portfolio Grid - Use masonry columns */
    .portfolio-grid {
        columns: 3 !important;
        column-gap: 0.75rem !important;
        padding: 1rem 0 !important;
    }

    .portfolio-item {
        margin-bottom: 1rem !important;
        break-inside: avoid !important;
        width: 100% !important;
    }

    /* Fix portfolio filter display on mobile */
    .portfolio-item[style*="display: block"] {
        display: inline-block !important;
    }

    .portfolio-item[style*="display: none"] {
        display: none !important;
    }

    .portfolio-image {
        height: auto !important;
        min-height: auto !important;
        background: transparent !important;
    }

    .portfolio-main-image {
        height: auto !important;
        max-height: 250px !important;
        object-fit: cover !important;
    }

    .video-item .portfolio-image {
        height: 180px !important;
    }

    .video-item .portfolio-main-video {
        height: 180px !important;
        object-fit: cover !important;
    }

    .filter-tabs {
        gap: 0.5rem !important;
        padding: 0 1rem !important;
    }

    .filter-btn {
        padding: 0.5rem 1rem !important;
        font-size: 0.9rem !important;
    }

    /* Portfolio Section Padding */
    .portfolio-section {
        padding: 2rem 0 !important;
    }

    .portfolio-filters {
        padding: 1.5rem 0 !important;
    }

    /* Portfolio Overlay Mobile Fix */
    .portfolio-overlay {
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
    }

    .portfolio-item:hover .portfolio-overlay {
        opacity: 1 !important;
    }

    /* Portfolio Content Mobile */
    .portfolio-content {
        padding: 1rem !important;
    }

    .portfolio-title {
        font-size: 1.2rem !important;
    }

    .portfolio-description {
        font-size: 0.9rem !important;
        line-height: 1.5 !important;
    }

    .portfolio-tags {
        gap: 0.25rem !important;
    }

    .portfolio-tags span {
        font-size: 0.7rem !important;
        padding: 0.2rem 0.5rem !important;
    }
}

@media (max-width: 480px) {
    /* Portfolio Stats - Single Row */
    .portfolio-stats .stats-grid {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 0.5rem !important;
    }

    .portfolio-stats .stat-item {
        padding: 1rem 0.5rem !important;
        text-align: center !important;
    }

    .portfolio-stats .stat-number {
        font-size: 1.5rem !important;
    }

    .portfolio-stats .stat-label {
        font-size: 0.7rem !important;
        line-height: 1.2 !important;
    }

    /* Portfolio Grid - Two Columns */
    .portfolio-grid {
        columns: 2 !important;
        column-gap: 0.5rem !important;
        padding: 1rem !important;
    }

    .portfolio-item {
        margin-bottom: 1.5rem !important;
        width: 100% !important;
    }

    /* Fix portfolio filter display on small mobile */
    .portfolio-item[style*="display: block"] {
        display: inline-block !important;
    }

    .portfolio-item[style*="display: none"] {
        display: none !important;
    }

    .portfolio-image {
        height: auto !important;
        min-height: auto !important;
        background: transparent !important;
    }

    .portfolio-main-image {
        height: auto !important;
        max-height: 300px !important;
        object-fit: cover !important;
    }

    .video-item .portfolio-image {
        height: 200px !important;
    }

    .video-item .portfolio-main-video {
        height: 200px !important;
        object-fit: cover !important;
    }

    .portfolio-actions {
        flex-direction: row !important;
        gap: 1rem !important;
        justify-content: center !important;
    }

    .filter-tabs {
        flex-direction: column !important;
        gap: 0.5rem !important;
        align-items: center !important;
    }

    .filter-btn {
        width: 200px !important;
        text-align: center !important;
        padding: 0.75rem 1rem !important;
    }

    /* Very small screens - stack hero features in 2x2 grid */
    .hero-features {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 0.5rem !important;
    }

    .hero-features .feature-item {
        padding: 0.4rem 0.6rem !important;
        gap: 0.3rem !important;
    }

    .hero-features .feature-item span {
        font-size: 0.65rem !important;
        color: white !important;
        font-weight: 600 !important;
    }

    .hero-features .feature-item i {
        font-size: 0.8rem !important;
        color: white !important;
    }

    /* Content features for small screens - use 2 columns to prevent overlap */
    .content-features {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 0.75rem !important;
    }

    .content-features .feature-item {
        flex-direction: row !important;
        text-align: left !important;
        gap: 0.5rem !important;
        font-size: 0.8rem !important;
        padding: 0.5rem !important;
    }

    .content-features .feature-item i {
        font-size: 1rem !important;
        flex-shrink: 0 !important;
    }

    .content-features .feature-item span {
        line-height: 1.2 !important;
    }
}

/* ===== IMAGE MODAL ===== */
.image-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.modal-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content img,
.modal-content video {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
}

.modal-content video {
    background: #000;
}

.close-modal {
    position: absolute;
    top: -40px;
    right: -40px;
    color: white;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    background: rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: var(--purple-primary);
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .close-modal {
        top: -30px;
        right: -30px;
        width: 30px;
        height: 30px;
        font-size: 1.5rem;
    }

    .image-modal {
        padding: 1rem;
    }
}

/* Service Page Styles */
.service-hero {
    padding: 10rem 0 4rem;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
}

.service-hero::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: radial-gradient(ellipse at center, rgba(139, 92, 246, 0.05) 0%, transparent 70%);
    pointer-events: none;
}

.service-hero-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin: 2rem 0;
}

.hero-features .feature-item {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-width: 0;
    flex: 1;
    background: var(--orange-primary);
    border-radius: 25px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(255, 107, 53, 0.2);
}

.hero-features .feature-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.hero-features .feature-item span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.85rem;
    font-weight: 600;
    color: white;
}

.hero-features .feature-item i {
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.service-icon-large {
    width: 200px;
    height: 200px;
    background: var(--purple-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 5rem;
    margin: 0 auto;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
}

.service-icon-large:hover {
    transform: translateY(-8px) scale(1.05);
}

.services-overview {
    padding: var(--section-padding);
    background: var(--bg-primary);
}

.technologies-section {
    padding: var(--section-padding);
    background: var(--bg-secondary);
    display: block;
    visibility: visible;
}

.tech-categories {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    width: 100%;
    visibility: visible;
}

.tech-category {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    display: block;
    visibility: visible;
    min-height: 250px;
    overflow: visible;
    box-sizing: border-box;
    transition: var(--transition-normal);
}

.tech-category:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--purple-primary);
}

.tech-category h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    text-align: center;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-auto-rows: auto;
    gap: 0.75rem;
    width: 100%;
    box-sizing: border-box;
    overflow: visible;
}

.tech-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
    padding: 0.75rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    transition: var(--transition-normal);
    text-align: center;
    min-height: 70px;
    box-sizing: border-box;
    width: 100%;
}

.tech-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: var(--shadow-lg);
}

.tech-item i {
    font-size: 1.5rem;
    color: var(--purple-primary);
}

.tech-item span {
    font-size: 0.8rem;
    font-weight: 500;
    color: var(--text-primary);
    line-height: 1.2;
}

.process-section {
    padding: var(--section-padding);
    background: var(--bg-primary);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

/* Force 2 columns on mobile for process steps */
@media (max-width: 768px) {
    .process-steps {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1.5rem !important;
    }
}

@media (max-width: 480px) {
    .process-steps {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem !important;
    }

    .process-step {
        padding: 1.25rem !important;
    }

    .step-number {
        font-size: 1.5rem !important;
        width: 45px !important;
        height: 45px !important;
        line-height: 45px !important;
    }

    .step-content h3 {
        font-size: 1rem !important;
        margin-bottom: 0.75rem !important;
    }

    .step-content p {
        font-size: 0.85rem !important;
        line-height: 1.4 !important;
    }
}

.process-step {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    position: relative;
    transition: var(--transition-normal);
}

.process-step:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--orange-primary);
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--orange-primary);
    color: var(--text-white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 800;
    margin: 0 auto 1.5rem;
}

.step-content h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.step-content p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.why-amazon {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.amazon-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.amazon-stats .stat-item {
    text-align: center;
    padding: 1.5rem;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
}

.amazon-stats .stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--orange-primary);
    margin-bottom: 0.5rem;
}

.amazon-stats .stat-label {
    color: var(--text-secondary);
    font-weight: 500;
}

/* Service Page Responsive */
@media (max-width: 1024px) {
    .tech-categories {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .hero-features {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .hero-features .feature-item {
        padding: 0.6rem 0.8rem;
    }

    .hero-features .feature-item span {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .service-hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-features {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .hero-features .feature-item {
        flex-direction: row;
        text-align: center;
        gap: 0.4rem;
        min-width: 0;
        padding: 0.5rem 0.75rem;
        justify-content: center;
    }

    .hero-features .feature-item i {
        font-size: 0.9rem;
    }

    .hero-features .feature-item span {
        font-size: 0.7rem;
        line-height: 1.1;
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
        word-break: break-word;
        hyphens: auto;
        color: white;
        font-weight: 600;
    }

    .hero-actions {
        justify-content: center;
    }

    .service-icon-large {
        width: 150px;
        height: 150px;
        font-size: 4rem;
    }

    .tech-categories {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .tech-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }

    .tech-item {
        padding: 0.5rem;
        min-height: 60px;
    }

    .tech-item i {
        font-size: 1.2rem;
    }

    .tech-item span {
        font-size: 0.7rem;
    }

    .process-steps {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem !important;
    }

    .process-step {
        padding: 1rem !important;
    }

    .step-number {
        font-size: 1.25rem !important;
        width: 40px !important;
        height: 40px !important;
        line-height: 40px !important;
        margin-bottom: 0.75rem !important;
    }

    .step-content h3 {
        font-size: 0.9rem !important;
        margin-bottom: 0.5rem !important;
        line-height: 1.3 !important;
    }

    .step-content p {
        font-size: 0.75rem !important;
        line-height: 1.3 !important;
        margin: 0 !important;
    }

    .amazon-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .tech-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.4rem;
    }

    .tech-item {
        padding: 0.4rem;
        min-height: 55px;
    }

    .tech-item i {
        font-size: 1rem;
    }

    .tech-item span {
        font-size: 0.65rem;
        line-height: 1.1;
    }
}

/* AI Benefits Section */
.ai-benefits {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    transition: var(--transition-normal);
}

.benefit-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--purple-primary);
}

.benefit-icon {
    width: 70px;
    height: 70px;
    background: var(--purple-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.8rem;
    transition: all 0.3s ease;
}

.benefit-card:hover .benefit-icon {
    transform: translateY(-8px) scale(1.1);
}

.benefit-card h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.benefit-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Marketing Results Section */
.marketing-results {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

/* Ensure 2 columns on all screen sizes */
@media (max-width: 768px) {
    .results-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1.5rem !important;
        max-width: 100% !important;
    }

    .result-card {
        padding: 1rem !important;
        padding-top: 3rem !important;
    }

    .result-icon {
        width: 50px !important;
        height: 50px !important;
        font-size: 1.1rem !important;
    }

    .result-number {
        font-size: 1.75rem !important;
        margin-bottom: 0.5rem !important;
    }

    .result-label {
        font-size: 0.85rem !important;
        margin-bottom: 0.5rem !important;
    }

    .result-card p {
        font-size: 0.75rem !important;
        line-height: 1.4 !important;
    }
}

@media (max-width: 480px) {
    .results-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem !important;
        padding: 0 1rem !important;
    }

    .result-card {
        padding: 0.75rem !important;
        padding-top: 2.5rem !important;
    }

    .result-icon {
        width: 45px !important;
        height: 45px !important;
        font-size: 1rem !important;
        top: -1rem !important;
    }

    .result-number {
        font-size: 1.5rem !important;
        margin-top: 0.5rem !important;
        margin-bottom: 0.25rem !important;
    }

    .result-label {
        font-size: 0.75rem !important;
        margin-bottom: 0.25rem !important;
        line-height: 1.2 !important;
    }

    .result-card p {
        font-size: 0.7rem !important;
        line-height: 1.3 !important;
        margin: 0 !important;
    }
}

.result-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    transition: var(--transition-normal);
}

.result-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--orange-primary);
}

.result-number {
    font-size: 3rem;
    font-weight: 800;
    color: var(--orange-primary);
    margin-top: 1rem;
    margin-bottom: 0.5rem;
}

.result-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.result-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Design Principles Section */
.design-principles {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.principles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.principle-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    transition: var(--transition-normal);
}

.principle-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--purple-primary);
}

.principle-icon {
    width: 70px;
    height: 70px;
    background: var(--orange-primary);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.8rem;
    transition: all 0.3s ease;
}

.principle-card:hover .principle-icon {
    transform: translateY(-8px) scale(1.1);
}

.principle-card h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.principle-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Legal Pages Styles */
.legal-content {
    padding: var(--section-padding);
    background: var(--bg-primary);
}

.legal-document {
    max-width: 800px;
    margin: 0 auto;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 3rem;
    box-shadow: var(--shadow-md);
}

.document-meta {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 2rem;
}

.document-meta p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.document-section {
    margin-bottom: 2.5rem;
}

.document-section h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--purple-primary);
}

.document-section p {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1rem;
}

.document-section ul {
    margin: 1rem 0;
    padding-left: 2rem;
}

.document-section li {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.contact-info {
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 1rem;
}

.contact-info p {
    margin: 0.5rem 0;
}

.contact-info a {
    color: var(--purple-primary);
    text-decoration: none;
    transition: var(--transition-normal);
}

.contact-info a:hover {
    color: var(--purple-secondary);
    text-decoration: underline;
}

/* Additional Responsive Styles - Only for very small screens */
@media (max-width: 480px) {
    .benefits-grid,
    .principles-grid {
        grid-template-columns: 1fr;
    }

    .results-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem !important;
        max-width: 100% !important;
    }

    .result-number {
        font-size: 2.5rem;
    }

    .benefit-icon,
    .principle-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .legal-document {
        padding: 2rem 1.5rem;
    }

    .document-section h2 {
        font-size: 1.3rem;
    }
}

/* ===== FINAL MOBILE RESPONSIVE FIXES ===== */
/* These styles ensure everything works properly on mobile devices */

/* CRITICAL MOBILE MENU FIX */
@media (max-width: 768px) {
    /* Force mobile menu button to be visible and clickable */
    .mobile-menu-toggle {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
        position: relative !important;
        z-index: 10001 !important;
        background: transparent !important;
        border: none !important;
        cursor: pointer !important;
        padding: 10px !important;
        margin-left: auto !important;
        order: 999 !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        width: 44px !important;
        height: 44px !important;
        gap: 4px !important;
    }

    /* Ensure hamburger lines are visible */
    .mobile-menu-toggle span {
        display: block !important;
        width: 25px !important;
        height: 3px !important;
        background-color: var(--text-primary) !important;
        border-radius: 2px !important;
        transition: all 0.3s ease !important;
        margin: 0 !important;
    }

    /* Ensure mobile menu works */
    .mobile-menu {
        display: block !important;
        visibility: visible !important;
    }

    /* Hide desktop navigation completely */
    .nav-menu,
    .user-menu,
    .auth-actions {
        display: none !important;
        visibility: hidden !important;
    }

    /* Ensure nav-actions container is visible for mobile menu toggle */
    .nav-actions {
        display: flex !important;
        visibility: visible !important;
        align-items: center !important;
        justify-content: flex-end !important;
        margin-left: auto !important;
    }

    /* Fix hero stats display */
    .hero-stats {
        display: flex !important;
        flex-direction: row !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 1.5rem !important;
        margin-top: 2rem !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100% !important;
    }

    .hero-stats .stat-item {
        flex: 1 !important;
        min-width: 80px !important;
        max-width: 120px !important;
        text-align: center !important;
    }

    .hero-stats .stat-number {
        font-size: 1.5rem !important;
        font-weight: 700 !important;
        color: var(--purple-primary) !important;
        margin-bottom: 0.25rem !important;
        line-height: 1.2 !important;
    }

    .hero-stats .stat-label {
        font-size: 0.75rem !important;
        color: var(--text-secondary) !important;
        font-weight: 500 !important;
        line-height: 1.2 !important;
    }

    /* Center all content */
    .hero-content {
        text-align: center !important;
        align-items: center !important;
    }

    .about-content {
        text-align: center !important;
        align-items: center !important;
    }

    /* Remove floating cards completely */
    .floating-card,
    .hero-visual,
    .hero-main-visual {
        display: none !important;
    }
}

/* Very small screens additional fixes */
@media (max-width: 480px) {
    .hero-stats {
        gap: 1rem !important;
    }

    .hero-stats .stat-item {
        min-width: 70px !important;
        max-width: 100px !important;
    }

    .hero-stats .stat-number {
        font-size: 1.25rem !important;
    }

    .hero-stats .stat-label {
        font-size: 0.7rem !important;
    }

    .hero-title {
        font-size: 2rem !important;
    }

    .hero-description {
        font-size: 1rem !important;
    }
}

/* ===== MOBILE NAVIGATION POSITIONING FIX ===== */
@media (max-width: 768px) {
    /* CRITICAL: Prevent horizontal scrolling */
    html, body {
        overflow-x: hidden !important;
        max-width: 100vw !important;
        box-sizing: border-box !important;
    }

    /* Container fixes to prevent overflow */
    .container {
        max-width: 100% !important;
        padding: 0 1rem !important;
        margin: 0 auto !important;
        box-sizing: border-box !important;
    }

    /* Header layout - ensure proper positioning */
    .header-content {
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
        padding: 1rem !important;
        padding-left: 1rem !important;
        padding-right: 1rem !important;
        position: relative !important;
        margin: 0 !important;
        max-width: 100% !important;
        width: 100% !important;
        box-sizing: border-box !important;
        z-index: 5 !important;
    }

    .logo {
        flex-shrink: 0 !important;
        order: 1 !important;
    }

    .logo-img {
        height: 35px !important;
    }

    /* Navigation actions container - properly positioned */
    .nav-actions {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-end !important;
        order: 3 !important;
        margin-left: auto !important;
        position: relative !important;
        z-index: 10001 !important;
    }

    /* Mobile menu toggle - properly positioned and sized */
    .mobile-menu-toggle {
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        width: 44px !important;
        height: 44px !important;
        background: transparent !important;
        border: none !important;
        cursor: pointer !important;
        padding: 8px !important;
        margin: 0 !important;
        position: relative !important;
        z-index: 10002 !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
        -webkit-tap-highlight-color: transparent !important;
        touch-action: manipulation !important;
        user-select: none !important;
        border-radius: 4px !important;
    }

    .mobile-menu-toggle:hover {
        background-color: rgba(139, 92, 246, 0.1) !important;
    }

    .mobile-menu-toggle:focus {
        outline: 2px solid var(--purple-primary) !important;
        outline-offset: 2px !important;
    }

    .mobile-menu-toggle span {
        display: block !important;
        width: 25px !important;
        height: 3px !important;
        background-color: var(--text-primary) !important;
        margin: 3px 0 !important;
        transition: all 0.3s ease !important;
        border-radius: 2px !important;
        transform-origin: center !important;
        pointer-events: none !important;
    }

    /* HERO SECTION FIXES */
    .hero {
        padding-top: 120px !important; /* Add space below fixed navbar */
        padding-bottom: 4rem !important;
        min-height: auto !important;
    }

    .hero-content {
        flex-direction: column !important;
        text-align: center !important;
        gap: 2rem !important;
    }

    .hero-text {
        order: 1 !important;
        width: 100% !important;
    }

    .hero-title {
        font-size: 2.2rem !important;
        line-height: 1.2 !important;
        margin-bottom: 1rem !important;
    }

    .hero-description {
        font-size: 1.1rem !important;
        line-height: 1.6 !important;
        margin-bottom: 1.5rem !important;
    }

    /* Hide the purple Digital Solutions box completely */
    .hero-main-visual {
        display: none !important;
    }

    /* PAGE HEADER FIXES */
    .page-header {
        padding: 120px 0 3rem !important; /* Add space below fixed navbar */
    }

    /* SERVICE HERO FIXES */
    .service-hero {
        padding: 120px 0 3rem !important; /* Add space below fixed navbar */
    }

    .visual-content,
    .visual-header {
        display: none !important;
    }

    /* Hero image section - keep floating cards but reorganize */
    .hero-image {
        order: 2 !important;
        width: 100% !important;
        margin-top: 1rem !important;
    }

    .hero-visual {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem !important;
        padding: 1rem !important;
        max-width: 400px !important;
        margin: 0 auto !important;
    }

    /* HERO STATS - FORCE ONE ROW LAYOUT */
    .hero-stats {
        display: flex !important;
        flex-direction: row !important;
        justify-content: center !important;
        align-items: center !important;
        gap: 1.5rem !important;
        margin-top: 2rem !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100% !important;
        flex-wrap: nowrap !important;
    }

    .hero-stats .stat-item {
        flex: 1 !important;
        min-width: 80px !important;
        max-width: 120px !important;
        text-align: center !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
    }

    .hero-stats .stat-number {
        font-size: 1.5rem !important;
        font-weight: 700 !important;
        color: var(--purple-primary) !important;
        margin-bottom: 0.25rem !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
    }

    .hero-stats .stat-label {
        font-size: 0.75rem !important;
        color: var(--text-secondary) !important;
        font-weight: 500 !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
        text-align: center !important;
    }

    /* Mobile menu close button - properly positioned */
    .mobile-menu-close {
        position: absolute !important;
        top: 1rem !important;
        right: 1rem !important;
        background: transparent !important;
        border: none !important;
        font-size: 1.5rem !important;
        color: var(--text-primary) !important;
        cursor: pointer !important;
        z-index: 10001 !important;
        width: 44px !important;
        height: 44px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: var(--transition-normal) !important;
        border-radius: 50% !important;
        padding: 0 !important;
    }

    .mobile-menu-close:hover {
        background-color: var(--bg-hover) !important;
        color: var(--purple-primary) !important;
    }

    /* Mobile menu actions - properly styled */
    .mobile-menu-actions {
        margin-top: 2rem !important;
        padding-top: 2rem !important;
        border-top: 1px solid var(--border-primary) !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 1rem !important;
    }

    .btn-full {
        width: 100% !important;
        justify-content: center !important;
        text-align: center !important;
        padding: 1rem !important;
        font-size: 1rem !important;
    }
}

/* ===== CRITICAL HERO STATS HORIZONTAL FIX ===== */
@media (max-width: 768px) {
    /* Override all previous hero-stats rules with maximum specificity */
    .hero .hero-content .hero-text .hero-stats,
    .hero-stats {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
        gap: 1rem !important;
        margin-top: 2rem !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100% !important;
        flex-wrap: nowrap !important;
        padding: 0 !important;
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    /* Force stat items to be in a row */
    .hero .hero-content .hero-text .hero-stats .stat-item,
    .hero-stats .stat-item {
        flex: 1 !important;
        min-width: 0 !important;
        max-width: none !important;
        text-align: center !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        margin: 0 !important;
        padding: 0.5rem !important;
        background: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    /* Style the numbers */
    .hero .hero-content .hero-text .hero-stats .stat-item .stat-number,
    .hero-stats .stat-item .stat-number {
        font-size: 1.5rem !important;
        font-weight: 700 !important;
        color: var(--purple-primary) !important;
        margin-bottom: 0.25rem !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
        display: block !important;
    }

    /* Style the labels */
    .hero .hero-content .hero-text .hero-stats .stat-item .stat-label,
    .hero-stats .stat-item .stat-label {
        font-size: 0.75rem !important;
        color: var(--text-secondary) !important;
        font-weight: 500 !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
        text-align: center !important;
        display: block !important;
    }

    /* ===== STATS CARDS - FORCE ONE ROW LAYOUT ===== */
    .about-visual .stats-card,
    .stats-card {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
        gap: 1rem !important;
        flex-wrap: nowrap !important;
        padding: 1rem !important;
        margin: 0.5rem 0 !important;
        background: transparent !important;
        border: none !important;
        border-radius: var(--border-radius) !important;
        box-shadow: none !important;
    }

    /* Reduce spacing around about visual container */
    .about-visual {
        height: auto !important;
        min-height: auto !important;
        padding: 1rem !important;
        margin: 0 !important;
    }

    .about-visual .stats-card .stat-item,
    .stats-card .stat-item {
        flex: 1 !important;
        text-align: center !important;
        min-width: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .about-visual .stats-card .stat-number,
    .stats-card .stat-number {
        font-size: 1.5rem !important;
        font-weight: 800 !important;
        color: var(--purple-primary) !important;
        margin-bottom: 0.25rem !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
    }

    .about-visual .stats-card .stat-label,
    .stats-card .stat-label {
        font-size: 0.75rem !important;
        color: var(--text-secondary) !important;
        font-weight: 500 !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
    }

    /* ===== ABOUT FEATURES - FORCE ONE ROW LAYOUT ===== */
    .about-text .about-features,
    .about-features {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: center !important;
        gap: 0.5rem !important;
        flex-wrap: nowrap !important;
        margin-bottom: 1rem !important;
        width: 100% !important;
    }

    .about-text .about-features .feature-item,
    .about-features .feature-item {
        flex: 1 !important;
        flex-direction: column !important;
        text-align: center !important;
        gap: 0.25rem !important;
        align-items: center !important;
        min-width: 0 !important;
        padding: 0.5rem 0.25rem !important;
        margin: 0 !important;
    }

    .about-text .about-features .feature-item i,
    .about-features .feature-item i {
        font-size: 1rem !important;
        margin-bottom: 0.25rem !important;
        color: var(--orange-primary) !important;
    }

    .about-text .about-features .feature-item span,
    .about-features .feature-item span {
        font-size: 0.7rem !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        font-weight: 500 !important;
        color: var(--text-primary) !important;
    }
}

/* ===== AMAZON MEGA MENU STYLES ===== */
.amazon-mega-menu {
    position: relative;
}

.amazon-mega-dropdown {
    position: absolute;
    top: 100%;
    left: -500px;
    background: var(--bg-card);
    border: none;
    border-radius: 0;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    z-index: 9999;
    margin-top: 0;
    width: 1400px;
    max-width: 95vw;
    min-height: 500px;
}

.amazon-mega-menu:hover .amazon-mega-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mega-menu-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0;
    padding: 3rem 2rem;
    background: var(--bg-card);
    width: 100%;
    height: 100%;
}

.mega-menu-column {
    padding: 0 2rem;
    border-right: 1px solid #e5e7eb;
    min-height: 400px;
}

.mega-menu-column:last-child {
    border-right: none;
}

.mega-menu-title {
    font-size: 0.9rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0;
    border-bottom: none;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.mega-menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
}

.mega-menu-list li {
    margin-bottom: 0;
    display: block;
    width: 100%;
}

.mega-menu-list a {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 1;
    transition: all var(--transition-fast);
    display: block;
    padding: 0.2rem 0 0.2rem 1rem;
    position: relative;
}

.mega-menu-list a:hover {
    color: var(--orange-primary);
}

.mega-menu-list a::before {
    content: '•';
    color: var(--orange-primary);
    font-weight: bold;
    font-size: 1rem;
    position: absolute;
    left: 0;
    top: 0.4rem;
    line-height: 1;
}

/* Responsive adjustments for mega menu */
@media (max-width: 1600px) {
    .amazon-mega-dropdown {
        left: -450px;
        width: 1200px;
    }

    .mega-menu-container {
        grid-template-columns: repeat(4, 1fr);
        padding: 2.5rem 1.5rem;
    }

    .mega-menu-column {
        padding: 0 1.5rem;
    }
}

@media (max-width: 1400px) {
    .amazon-mega-dropdown {
        left: -400px;
        width: 1000px;
    }

    .mega-menu-container {
        grid-template-columns: repeat(3, 1fr);
        padding: 2rem 1.5rem;
    }

    .mega-menu-column {
        padding: 0 1.25rem;
    }
}

@media (max-width: 1200px) {
    .amazon-mega-dropdown {
        left: -350px;
        width: 900px;
    }

    .mega-menu-container {
        grid-template-columns: repeat(2, 1fr);
        padding: 2rem 1rem;
        min-height: 400px;
    }

    .mega-menu-column {
        padding: 0 1rem;
        min-height: 350px;
    }
}

@media (max-width: 992px) {
    .amazon-mega-dropdown {
        left: -300px;
        width: 750px;
    }

    .mega-menu-container {
        grid-template-columns: repeat(2, 1fr);
        padding: 1.5rem 1rem;
        min-height: 350px;
    }

    .mega-menu-column {
        padding: 0 0.75rem;
        min-height: 300px;
    }
}

@media (max-width: 768px) {
    .amazon-mega-dropdown {
        display: none;
    }
}

/* FAQ Section Styles */
.faq-section {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
}

.faq-item:hover {
    box-shadow: var(--shadow-lg);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-card);
    transition: var(--transition-normal);
}

.faq-question:hover {
    background: var(--bg-hover);
}

.faq-question h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.faq-question i {
    color: var(--purple-primary);
    transition: transform 0.3s ease;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: var(--bg-primary);
}

.faq-answer p {
    padding: 1.5rem;
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.6;
}

.faq-answer ul {
    padding: 0 1.5rem 1.5rem 1.5rem;
    margin: 0;
    list-style: none;
}

.faq-answer ul li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
    line-height: 1.6;
}

.faq-answer ul li:before {
    content: "•";
    color: var(--purple-primary);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.faq-answer ul li strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* Multiple FAQ Sections Styling */
.faq-section + .faq-section {
    padding-top: 0;
    margin-top: -2rem;
}

.faq-section + .faq-section .section-header {
    margin-bottom: 3rem;
    padding-top: 3rem;
    border-top: 2px solid var(--border-color);
}

.faq-section .section-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-top: 1rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Who Can Benefit Section Styling */
.content-grid.content-centered {
    text-align: center;
    max-width: 1000px;
    margin: 0 auto;
}

.content-grid.content-centered .content-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2rem;
    justify-items: center;
}

.content-grid.content-centered .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    max-width: 250px;
}

.content-grid.content-centered .feature-icon {
    width: 100px;
    height: 100px;
    background: var(--purple-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    color: white;
    font-size: 2.5rem;
}

.content-grid.content-centered .feature-content h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.content-grid.content-centered .feature-content p {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.5;
}

.faq-item.active .faq-answer {
    max-height: 200px;
}

/* Content List Styles */
.content-list {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.content-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    line-height: 1.6;
}

.content-list li i {
    margin-right: 1rem;
    margin-top: 0.2rem;
    font-size: 1rem;
}

/* CTA Section Styles */
.cta-section {
    padding: var(--section-padding);
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
    color: white;
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
}

.cta-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    color: white;
}

.cta-description a {
    color: white !important;
    text-decoration: underline;
    font-weight: 600;
}

.cta-description a:hover {
    color: #f0f0f0 !important;
    text-decoration: none;
}

.cta-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-actions .btn {
    min-width: 150px;
}

.cta-actions .btn-outline {
    border-color: white;
    color: white;
}

.cta-actions .btn-outline:hover {
    background: white;
    color: var(--purple-primary);
}

/* Result Definition Styles */
.result-definition {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.result-definition strong {
    color: var(--purple-primary);
}

/* Brand Registry Page Image Styles */
.hero-image {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

.content-image {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
}

.service-hero .hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.content-section .content-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Brand Registry Page Responsive Styles */
@media (max-width: 768px) {
    .cta-title {
        font-size: 2rem;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }

    .faq-question h4 {
        font-size: 1rem;
    }

    .content-list li {
        font-size: 1rem;
    }

    .cta-description {
        font-size: 1.1rem;
    }

    .hero-image {
        max-width: 350px;
    }

    .content-image {
        max-width: 400px !important;
        width: 100% !important;
        margin: 0 auto !important;
        display: block !important;
    }

    .content-grid.content-centered .content-features {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .content-grid.content-centered .feature-item {
        max-width: 300px;
    }
}

@media (max-width: 992px) {
    .content-grid.content-centered .content-features {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (max-width: 640px) {
    .content-grid.content-centered .content-features {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .content-grid.content-centered .feature-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .content-grid.content-centered .feature-content h4 {
        font-size: 1.1rem;
    }
}

/* FAQ Image Styles */
.faq-image-container {
    text-align: center;
    margin-bottom: 3rem;
}

.faq-image {
    max-width: 60%;
    height: auto;
    max-height: 250px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    object-fit: cover;
}

@media (max-width: 768px) {
    .faq-image-container {
        margin-bottom: 2rem;
    }

    .faq-image {
        max-width: 80%;
        max-height: 180px;
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .cta-title {
        font-size: 1.8rem;
    }

    .faq-question {
        padding: 1rem;
    }

    .faq-answer p {
        padding: 1rem;
    }

    .cta-actions .btn {
        min-width: 120px;
        font-size: 0.9rem;
    }

    .hero-image {
        max-width: 380px !important;
        transform: scale(1.0) !important;
    }

    .content-image {
        max-width: 350px !important;
        width: 100% !important;
        margin: 0 auto !important;
        display: block !important;
    }

    .service-hero .hero-visual,
    .content-section .content-visual {
        margin-top: 2rem;
    }
}

/* FBA Page Styles */
.incentives-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.incentive-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.incentive-card:hover {
    box-shadow: var(--shadow-lg);
}

.incentive-amount {
    font-size: 2rem;
    font-weight: 700;
    color: var(--purple-primary);
    margin-bottom: 1rem;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.benefit-card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.benefit-card:hover {
    box-shadow: var(--shadow-lg);
}

.benefit-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.benefit-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.fba-process {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.process-step {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
    padding: 2rem;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.process-step:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    flex-shrink: 0;
}

.step-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.process-note {
    text-align: center;
    margin-top: 2rem;
    padding: 1.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--purple-primary);
}

.fba-visual-process {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 0;
}

.visual-step {
    text-align: center;
    flex: 1;
    min-width: 180px;
    max-width: 220px;
}

.visual-icon {
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2.5rem;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.visual-icon:hover {
    transform: scale(1.05);
}

.visual-step h4 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

.visual-step p {
    font-size: 0.95rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.visual-arrow {
    color: var(--purple-primary);
    font-size: 2rem;
    flex-shrink: 0;
    margin: 0 0.5rem;
    opacity: 0.7;
}

.subsection-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .incentives-grid,
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .fba-process {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .process-step {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .fba-visual-process {
        flex-direction: column;
        gap: 2rem;
        padding: 1rem 0;
    }

    .visual-step {
        min-width: auto;
        max-width: 300px;
        margin: 0 auto;
    }

    .visual-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .visual-arrow {
        transform: rotate(90deg);
        font-size: 1.5rem;
        margin: 0;
    }
}

/* New FBA Process Steps Styles */
.fba-process-steps {
    max-width: 1000px;
    margin: 0 auto;
}

.process-step-detailed {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    margin-bottom: 3rem;
    padding: 2.5rem;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border-left: 4px solid var(--purple-primary);
}

.process-step-detailed:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.step-icon {
    position: relative;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    flex-shrink: 0;
    box-shadow: var(--shadow-lg);
}

.step-number {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 28px;
    height: 28px;
    background: var(--yellow-primary);
    color: var(--text-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    font-weight: 700;
    box-shadow: var(--shadow-md);
}

.step-content h3 {
    font-size: 1.6rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.step-description p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.step-description p:last-child {
    margin-bottom: 0;
}

.highlight-badge {
    background: var(--yellow-primary);
    color: var(--text-primary);
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    margin-left: 0.5rem;
}

.highlight-text {
    background: var(--bg-tertiary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--purple-primary);
    margin: 2rem 0;
}

.fee-explanation {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    margin-top: 2rem;
    border: 1px solid var(--border-primary);
}

.fee-explanation h3 {
    color: var(--purple-primary);
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

/* Pricing Tabs Container */
.pricing-table-container {
    max-width: 1200px;
    margin: 0 auto;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

/* Pricing Tabs Navigation */
.pricing-tabs {
    display: flex;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 2px solid var(--border-primary);
    margin: 0;
    padding: 0;
    position: relative;
}

.pricing-tabs::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--border-primary);
    z-index: 1;
}

.pricing-tab {
    flex: 1;
    padding: 1.25rem 1.5rem;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
    position: relative;
    z-index: 2;
    text-align: center;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid var(--border-primary);
}

.pricing-tab:last-child {
    border-right: none;
}

.pricing-tab:hover {
    background: linear-gradient(135deg, var(--bg-tertiary), var(--bg-secondary));
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.pricing-tab.active {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
    color: white;
    border-bottom: 3px solid var(--yellow-primary);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 69, 255, 0.3);
    font-weight: 700;
}

.pricing-tab.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--yellow-primary);
    z-index: 3;
}

/* Tab Text Styling */
.pricing-tab span {
    display: block;
    line-height: 1.3;
}

.pricing-tab .tab-main {
    font-size: 1rem;
    font-weight: 700;
}

.pricing-tab .tab-sub {
    font-size: 0.85rem;
    opacity: 0.8;
    margin-top: 2px;
}

/* Ensure first table is visible by default */
#non-apparel {
    display: block;
}

/* Tab Content Header */
.pricing-table-content::before {
    content: '';
    display: block;
    height: 1px;
    background: linear-gradient(90deg, var(--purple-primary), var(--yellow-primary), var(--purple-primary));
    margin: 0;
}

/* Pricing Table Container with Scroll */
.pricing-table-content {
    display: none;
    background: var(--bg-card);
    padding: 0;
    max-height: 600px;
    overflow-y: auto;
    position: relative;
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

.pricing-table-content.active {
    display: block;
}

/* Custom Scrollbar */
.pricing-table-content::-webkit-scrollbar {
    width: 8px;
}

.pricing-table-content::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

.pricing-table-content::-webkit-scrollbar-thumb {
    background: var(--purple-primary);
    border-radius: 4px;
}

.pricing-table-content::-webkit-scrollbar-thumb:hover {
    background: var(--purple-secondary);
}

/* FBA Pricing Table Styles */
.fba-pricing-table {
    width: 100%;
    border-collapse: collapse;
    background: #ffffff;
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    position: relative;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    overflow: hidden;
}

/* Ensure FBA pricing tables use exactly 3 columns */
.fba-pricing-table .pricing-header,
.fba-pricing-table .pricing-row {
    grid-template-columns: 1fr 1fr 1fr !important;
}

/* Table Animation */
.pricing-table-content.active .fba-pricing-table {
    animation: fadeInUp 0.5s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.pricing-header {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #374151;
    font-weight: 700;
    font-size: 1.1rem;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-bottom: 2px solid #d1d5db;
}

.pricing-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--yellow-primary), var(--purple-light), var(--yellow-primary));
}

.pricing-header .pricing-cell {
    padding: 1.75rem 1.5rem;
    text-align: center;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 70px;
    color: #374151;
    font-weight: 600;
}

.pricing-header .pricing-cell:last-child {
    border-right: none;
}

.pricing-header .pricing-cell::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    width: 1px;
    height: 30px;
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-50%);
}

.pricing-header .pricing-cell:last-child::before {
    display: none;
}

.pricing-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    border-bottom: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    position: relative;
    background: #ffffff;
}

.pricing-row:last-child {
    border-bottom: none;
}

.pricing-row:nth-child(even) {
    background: #f9fafb;
}



.pricing-cell {
    padding: 1.25rem 1.5rem;
    text-align: center;
    border-right: 1px solid #e5e7eb;
    vertical-align: middle;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    font-size: 0.95rem;
    line-height: 1.4;
    color: #374151;
}

.pricing-cell:last-child {
    border-right: none;
}

.category-cell {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: white;
    font-weight: 700;
    text-align: center;
    justify-content: center;
    font-size: 1rem;
    position: relative;
}

.category-cell::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--yellow-primary);
}

.category-row {
    border-bottom: 2px solid var(--purple-primary);
}



.price-cell {
    font-weight: 700;
    color: #1f2937;
    font-size: 1rem;
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    border: 1px solid #f59e0b;
}

/* Alternating row colors for better readability */
.pricing-row:nth-child(odd):not(.category-row) {
    background: white;
}

.pricing-row:nth-child(even):not(.category-row) {
    background: linear-gradient(135deg, #fafbfc, #f1f3f4);
}

/* Scroll Indicator */
.pricing-table-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.05));
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.pricing-table-content.scrollable::after {
    opacity: 1;
}

/* Sticky Header Enhancement */
.pricing-header.sticky {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Table Body Spacing */
.fba-pricing-table tbody {
    position: relative;
}

/* Enhanced Category Rows for Scroll */
.category-row {
    position: sticky;
    top: 70px;
    z-index: 5;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
}

.category-row .category-cell {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
}

/* Loading State */
.pricing-table-content.loading {
    opacity: 0.6;
    pointer-events: none;
}

.pricing-table-content.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-primary);
    border-top: 3px solid var(--purple-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    transform: translate(-50%, -50%);
    z-index: 10;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Tab Indicators */
.pricing-tab::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: transparent;
    transition: all 0.3s ease;
}

.pricing-tab.active::before {
    background: var(--yellow-primary);
}

/* Price Highlighting */
.price-cell:hover {
    background: linear-gradient(135deg, var(--yellow-light), var(--yellow-primary));
    color: var(--text-primary);
    transform: scale(1.02);
    z-index: 5;
    position: relative;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

/* Table Borders Enhancement */
.fba-pricing-table {
    border: 1px solid var(--border-primary);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* Category Row Enhancement */
.category-row .pricing-cell:not(.category-cell) {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    font-weight: 600;
    color: var(--text-primary);
}

@media (max-width: 768px) {
    .process-step-detailed {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
        padding: 2rem;
    }

    .step-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
        margin: 0 auto;
    }

    .step-number {
        width: 24px;
        height: 24px;
        font-size: 0.8rem;
    }

    .fba-pricing-table {
        font-size: 0.9rem;
    }

    .pricing-header,
    .pricing-row {
        grid-template-columns: 1fr;
    }

    .pricing-cell {
        padding: 0.75rem;
        border-right: none;
        border-bottom: 1px solid var(--border-primary);
    }

    .category-cell {
        background: var(--purple-primary);
        color: white;
        text-align: center;
        font-size: 1rem;
    }
}

/* Storage Fees Table */
.storage-fees-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    margin: 2rem 0;
}

.fee-note {
    background: var(--bg-tertiary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--yellow-primary);
    margin-top: 2rem;
}

/* New Seller Benefits */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.benefit-card-detailed {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border-top: 4px solid var(--purple-primary);
}

.benefit-card-detailed:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
}

.benefit-icon-detailed {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.benefit-card-detailed h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.benefit-card-detailed ul {
    list-style: none;
    padding: 0;
}

.benefit-card-detailed li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-primary);
}

.benefit-card-detailed li:last-child {
    border-bottom: none;
}

/* Program Details */
.program-details {
    max-width: 1000px;
    margin: 0 auto;
}

.program-section {
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    margin-bottom: 2rem;
    box-shadow: var(--shadow-md);
}

.program-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--purple-primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--purple-primary);
}

.program-benefit,
.requirement {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border-left: 4px solid var(--border-primary);
}

.requirement.important {
    border-left-color: var(--yellow-primary);
    background: var(--bg-tertiary);
}

.program-cta {
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
    color: white;
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    margin-top: 2rem;
}

.program-cta h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.program-cta p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

/* FAQ Styles */
.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    margin-bottom: 1rem;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: var(--transition-normal);
}

.faq-item:hover {
    box-shadow: var(--shadow-lg);
}

.faq-question {
    padding: 1.5rem 2rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-card);
    border-bottom: 1px solid var(--border-primary);
    transition: var(--transition-normal);
}

.faq-question:hover {
    background: var(--bg-secondary);
}

.faq-question h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.faq-question i {
    color: var(--purple-primary);
    transition: var(--transition-normal);
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 2rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 1.5rem 2rem;
    max-height: 200px;
}

.faq-answer p {
    margin: 0;
    line-height: 1.6;
    color: var(--text-secondary);
}

@media (max-width: 768px) {
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .benefit-card-detailed {
        padding: 1.5rem;
    }

    .program-section {
        padding: 2rem;
    }

    .program-cta {
        padding: 2rem;
    }

    .faq-question {
        padding: 1rem 1.5rem;
    }

    .faq-answer {
        padding: 0 1.5rem;
    }

    .faq-item.active .faq-answer {
        padding: 1rem 1.5rem;
    }

    /* Mobile Pricing Tabs */
    .pricing-tabs {
        flex-direction: column;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .pricing-tab {
        padding: 1rem;
        font-size: 0.9rem;
        text-align: center;
        border-right: none;
        border-bottom: 1px solid var(--border-primary);
        min-height: 50px;
    }

    .pricing-tab:last-child {
        border-bottom: none;
    }

    .pricing-tab.active {
        transform: none;
        box-shadow: inset 4px 0 0 var(--yellow-primary);
    }

    /* Mobile Pricing Table */
    .pricing-table-content {
        max-height: 500px;
        border-radius: 0 0 var(--border-radius) var(--border-radius);
    }

    .fba-pricing-table {
        font-size: 0.85rem;
    }

    .pricing-header {
        position: sticky;
        top: 0;
        z-index: 15;
    }

    .pricing-header,
    .pricing-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .pricing-header .pricing-cell {
        padding: 1rem;
        border-right: none;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        min-height: 50px;
    }

    .pricing-cell {
        padding: 1rem;
        border-right: none;
        border-bottom: 1px solid var(--border-primary);
        text-align: left;
        justify-content: flex-start;
        min-height: 50px;
    }

    .category-cell {
        background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
        color: white;
        text-align: center;
        font-size: 1rem;
        font-weight: 700;
        justify-content: center;
    }

    .category-cell::after {
        display: none;
    }

    .price-cell {
        font-weight: 700;
        color: var(--purple-primary);
        background: var(--bg-tertiary);
        border-left: 4px solid var(--yellow-primary);
    }



    /* Mobile Category Rows */
    .category-row {
        margin-top: 1rem;
        border-top: 2px solid var(--purple-primary);
    }

    .category-row:first-child {
        margin-top: 0;
        border-top: none;
    }
}

/* Understanding Amazon Fees Page Styles */
.fees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.fee-card {
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border-top: 4px solid var(--purple-primary);
}

.fee-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
}

.fee-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin-bottom: 1.5rem;
}

.fee-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.fee-details p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.fee-breakdown {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
    border-left: 4px solid var(--yellow-primary);
}

.fee-breakdown h4 {
    color: var(--purple-primary);
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.fee-breakdown ul {
    list-style: none;
    padding: 0;
}

.fee-breakdown li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-primary);
}

.fee-breakdown li:last-child {
    border-bottom: none;
}

.fee-note {
    background: var(--bg-tertiary);
    padding: 1rem;
    border-radius: var(--border-radius);
    font-style: italic;
    color: var(--text-secondary);
    border-left: 3px solid var(--purple-primary);
}

.important-notes {
    background: var(--yellow-light);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
    border: 1px solid var(--yellow-primary);
}

.important-notes h4 {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 1rem;
}

.highlight-text {
    background: var(--bg-tertiary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--purple-primary);
    margin: 2rem 0;
    font-weight: 600;
}

.warning-text {
    background: #fff3cd;
    color: #856404;
    padding: 1rem;
    border-radius: var(--border-radius);
    border: 1px solid #ffeaa7;
    margin: 1rem 0;
}

.critical-fee {
    color: #dc3545;
    font-weight: 700;
    background: #f8d7da;
    padding: 1rem;
    border-radius: var(--border-radius);
    border-left: 4px solid #dc3545;
}

/* Additional Fees Grid */
.additional-fees-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.fee-section {
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.fee-section:hover {
    box-shadow: var(--shadow-lg);
}

.fee-section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--purple-primary);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--purple-primary);
}

.fee-options {
    display: grid;
    gap: 1.5rem;
    margin: 2rem 0;
}

.fee-option {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--yellow-primary);
}

.fee-option h4 {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.media-categories {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
}

.media-categories h4 {
    color: var(--purple-primary);
    font-weight: 700;
    margin-bottom: 1rem;
}

.media-categories ul {
    list-style: none;
    padding: 0;
}

.media-categories li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-primary);
}

.media-categories li:last-child {
    border-bottom: none;
}

/* Size Tier Details */
.size-tier-details {
    display: grid;
    gap: 2rem;
    margin: 2rem 0;
}

.size-tier-item {
    background: var(--bg-card);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--purple-primary);
}

.size-tier-item h4 {
    color: var(--purple-primary);
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.size-tier-item ul {
    list-style: none;
    padding: 0;
}

.size-tier-item li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-primary);
}

.size-tier-item li:last-child {
    border-bottom: none;
}

/* Mobile Responsive for Fees Page */
@media (max-width: 768px) {
    .fees-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .fee-card {
        padding: 2rem;
    }

    .additional-fees-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .fee-section {
        padding: 2rem;
    }

    .size-tier-details {
        gap: 1.5rem;
    }

    .size-tier-item {
        padding: 1.5rem;
    }

    .fee-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .fee-card h3 {
        font-size: 1.2rem;
    }

    .fee-section-title {
        font-size: 1.3rem;
    }
}

/* Section Image Styling */
.section-image {
    max-width: 400px;
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    object-fit: cover;
}

/* FBA Program Header Layout */
.fba-program-header {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    margin-bottom: 2rem;
}

.program-title-container {
    flex: 1;
    text-align: center;
    z-index: 2;
}

.program-title-container .section-title {
    margin: 0;
    font-size: 2.5rem;
    font-weight: 700;
}

.program-image-container {
    position: absolute;
    right: -300px;
    top: 85%;
    transform: translateY(-50%);
    z-index: 1;
}

.program-image {
    max-width: 350px;
    max-height: 280px;
    width: auto;
    height: auto;
    object-fit: contain;
    background: white;
    padding: 0.75rem;
    border: 1px solid var(--border-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    opacity: 0.9;
}

@media (max-width: 768px) {
    .section-image {
        max-width: 300px;
        margin: 0 auto;
        display: block;
    }

    .fba-program-header {
        flex-direction: column;
        min-height: auto;
        gap: 1.5rem;
    }

    .program-title-container .section-title {
        font-size: 2rem;
    }

    .program-image-container {
        position: relative;
        right: auto;
        top: auto;
        transform: none;
        text-align: center;
    }

    .program-image {
        max-width: 200px;
        max-height: 150px;
    }
}

/* IP Accelerator Steps Styling */
.ip-accelerator-steps {
    display: grid;
    gap: 2rem;
    margin: 3rem 0;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 2rem;
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border-left: 4px solid var(--purple-primary);
}

.step-item:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.step-number {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--purple-primary), var(--purple-secondary));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
    box-shadow: var(--shadow-md);
}

.step-content {
    flex: 1;
}

.step-content h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.step-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.step-note {
    background: var(--yellow-light);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border-left: 4px solid var(--yellow-primary);
    margin-top: 1.5rem;
}

.step-note p {
    margin: 0;
    color: var(--text-primary);
    font-size: 0.95rem;
}

@media (max-width: 768px) {
    .step-item {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
        padding: 2rem;
    }

    .step-number {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin: 0 auto;
    }

    .step-content h3 {
        font-size: 1.2rem;
    }

    .step-note {
        padding: 1rem;
        text-align: left;
    }
}

/* Digital Marketing Animations & Styles */
.marketing-hero-image {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    z-index: 2;
}

.hero-main-image {
    max-width: 500px;
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.hero-main-image:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-xl);
}

.floating-stats {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden;
    z-index: 1;
}

.stat-bubble {
    position: absolute;
    background: var(--bg-card);
    border: 2px solid var(--purple-primary);
    border-radius: 50px;
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-lg);
    animation: float 3s ease-in-out infinite;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.stat-bubble:nth-child(1) {
    top: 15%;
    right: 5%;
    animation-delay: 0s;
}

.stat-bubble:nth-child(2) {
    top: 55%;
    left: 5%;
    animation-delay: 1s;
}

.stat-bubble:nth-child(3) {
    bottom: 25%;
    right: 10%;
    animation-delay: 2s;
}

.stat-icon {
    width: 30px;
    height: 30px;
    background: var(--purple-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
}

.stat-text {
    display: flex;
    flex-direction: column;
    line-height: 1.2;
}

.stat-number {
    font-weight: 700;
    font-size: 1rem;
    color: var(--purple-primary);
}

.stat-label {
    font-size: 0.7rem;
    color: var(--text-muted);
    font-weight: 500;
}



/* Marketing Metrics Section */
.marketing-metrics {
    padding: 5rem 0;
    background: var(--bg-secondary);
}

.metrics-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.metrics-text .section-title {
    margin-bottom: 1rem;
}

.metrics-text .section-description {
    margin-bottom: 2rem;
}

.metrics-image img {
    width: 100%;
    max-width: 400px;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.metrics-image img:hover {
    transform: scale(1.05);
}

.chart-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

/* Tablet responsive - 2 columns */
@media (max-width: 1024px) and (min-width: 769px) {
    .chart-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

.circular-chart {
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--bg-card);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
}

.circular-chart:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.circle-progress {
    position: relative;
    width: 140px;
    height: 140px;
}

.circle-svg {
    transform: rotate(-90deg);
    width: 100%;
    height: 100%;
    overflow: visible;
}

.circle-bg {
    fill: none;
    stroke: #e5e7eb;
    stroke-width: 12;
}

.circle-fill {
    fill: none;
    stroke: #F97316; /* Solid orange color */
    stroke-width: 12;
    stroke-linecap: round;
    stroke-dasharray: 377; /* 2 * π * 60 = 377 (exact circumference) */
    stroke-dashoffset: 377;
    transition: stroke-dashoffset 2.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.circle-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    width: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.circle-text .percentage {
    display: block;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--purple-primary);
    margin-bottom: 0.2rem;
    line-height: 1;
}

.circle-text .label {
    display: block;
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 600;
    line-height: 1.1;
    text-align: center;
    max-width: 70px;
}

/* Animate circles when in view - accurate percentage calculation */
.circular-chart.animate .circle-fill {
    stroke-dashoffset: calc(377 - (377 * var(--percentage) / 100));
}

/* Ensure proper circle sizing on all devices */
.circular-chart {
    position: relative;
}

.circle-progress {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.circle-text {
    pointer-events: none;
}



/* Marketing Objectives Styling */
.marketing-objectives {
    padding: 5rem 0;
    background: var(--bg-secondary);
}

.objectives-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

/* Mobile responsive for objectives grid */
@media (max-width: 768px) {
    .objectives-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1.5rem !important;
        margin-top: 2rem !important;
    }

    .objective-card {
        padding: 1.5rem !important;
    }

    .objective-icon {
        width: 45px !important;
        height: 45px !important;
        font-size: 1rem !important;
        margin-bottom: 0.75rem !important;
    }

    .objective-title {
        font-size: 1rem !important;
        margin-bottom: 0.75rem !important;
    }

    .objective-description {
        font-size: 0.85rem !important;
        line-height: 1.4 !important;
        margin-bottom: 1rem !important;
    }

    .objective-features {
        font-size: 0.8rem !important;
    }

    .objective-features li {
        margin-bottom: 0.5rem !important;
    }
}

@media (max-width: 480px) {
    .objectives-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem !important;
        margin-top: 1.5rem !important;
    }

    .objective-card {
        padding: 1rem !important;
    }

    .objective-icon {
        width: 35px !important;
        height: 35px !important;
        font-size: 0.85rem !important;
        margin-bottom: 0.5rem !important;
    }

    .objective-title {
        font-size: 0.9rem !important;
        margin-bottom: 0.5rem !important;
        line-height: 1.2 !important;
    }

    .objective-description {
        font-size: 0.75rem !important;
        line-height: 1.3 !important;
        margin-bottom: 0.75rem !important;
    }

    .objective-features {
        font-size: 0.7rem !important;
    }

    .objective-features li {
        margin-bottom: 0.25rem !important;
        line-height: 1.2 !important;
    }
}

.objective-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.objective-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
    border-color: var(--orange-primary);
}

.objective-icon {
    width: 80px;
    height: 80px;
    background: var(--purple-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem auto;
    color: white;
    font-size: 1.8rem;
    flex-shrink: 0;
    line-height: 1;
    text-align: center;
}

.objective-icon i {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    text-align: center;
    line-height: 1;
    font-weight: 900;
    position: relative;
}

/* Specific icon adjustments for perfect centering */
.objective-icon i.fa-chart-line {
    transform: translateX(-1px);
}

.objective-icon i.fa-bullseye {
    transform: translateX(0px);
}

.objective-icon i.fa-rocket {
    transform: translateX(1px);
}

.objective-icon i.fa-search,
.objective-icon i.fa-globe,
.objective-icon i.fa-users,
.objective-icon i.fa-dollar-sign {
    transform: translateX(0px);
}

.objective-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
}

.objective-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.3;
    text-align: center;
}

.objective-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 1rem;
    text-align: center;
    max-width: 100%;
}

.objective-features {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    text-align: left;
}

.objective-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--text-primary);
    font-size: 0.9rem;
    width: 100%;
    justify-content: flex-start;
}

.objective-features li i {
    color: var(--orange-primary);
    font-size: 0.8rem;
    flex-shrink: 0;
    width: 16px;
    text-align: center;
}

/* Growth Metrics Styling */
.growth-metrics {
    margin-bottom: 2rem;
}

.growth-metrics h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.growth-metrics p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.growth-stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.growth-stat {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.growth-stat:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.growth-stat .stat-icon {
    width: 50px;
    height: 50px;
    background: var(--purple-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.growth-stat .stat-content {
    flex: 1;
}

.growth-stat .stat-number {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--purple-primary);
    display: block;
    line-height: 1;
}

.growth-stat .stat-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Growth Rate Section */
.growth-rate-section {
    padding: 5rem 0;
    background: var(--bg-light);
}

.growth-rate-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 2rem;
    margin: 3rem 0;
    align-items: end;
}

.growth-rate-item {
    text-align: center;
    position: relative;
}

.growth-number {
    width: 40px;
    height: 40px;
    background: var(--purple-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    margin: 0 auto 1rem;
    box-shadow: var(--shadow-md);
}

.growth-bar {
    width: 60px;
    height: 200px;
    background: var(--border-color);
    border-radius: 30px;
    margin: 0 auto 1rem;
    position: relative;
    overflow: hidden;
}

.growth-bar .bar-fill {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, var(--purple-primary), var(--purple-light));
    border-radius: 30px;
    transition: height 2s cubic-bezier(0.4, 0, 0.2, 1);
    height: 0;
}

.growth-value {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--purple-primary);
    margin-bottom: 0.5rem;
}

.growth-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Growth Summary */
.growth-summary {
    margin-top: 4rem;
    text-align: center;
}

.summary-content h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.summary-content p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto 2rem;
    line-height: 1.6;
}

.growth-highlights {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.highlight-item i {
    color: var(--purple-primary);
    font-size: 1.1rem;
}

.highlight-item span {
    color: var(--text-primary);
    font-weight: 500;
}

/* Meeting Agenda Row */
.agenda-row {
    display: flex;
    justify-content: center;
    align-items: end;
    gap: 3rem;
    margin: 3rem 0;
    padding: 2rem 0;
}

.agenda-item {
    text-align: center;
    position: relative;
}

.agenda-number {
    width: 50px;
    height: 50px;
    background: var(--orange-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.3rem;
    margin: 0 auto 1rem;
    box-shadow: var(--shadow-md);
}

.agenda-bar {
    width: 40px;
    height: 150px;
    background: var(--border-color);
    border-radius: 20px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.agenda-bar .bar-fill {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: var(--orange-primary);
    border-radius: 20px;
    transition: height 2s cubic-bezier(0.4, 0, 0.2, 1);
    height: 0;
}

/* Marketing Results Section */
.results-content {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 4rem;
}

.results-visual {
    position: relative;
}

.results-image {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.results-image:hover {
    transform: scale(1.02);
}

.floating-metrics {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.metric-card {
    position: absolute;
    background: var(--bg-card);
    border: 2px solid var(--orange-primary);
    border-radius: 25px;
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-lg);
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--orange-primary);
    animation: bounce 2s ease-in-out infinite;
}

.metric-card:nth-child(1) {
    top: 20%;
    right: -10%;
    animation-delay: 0s;
}

.metric-card:nth-child(2) {
    bottom: 30%;
    left: -10%;
    animation-delay: 1s;
}

.bar-chart-container {
    background: var(--bg-card);
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    max-width: 900px;
    width: 100%;
}

.bar-chart-container h3 {
    margin-bottom: 2rem;
    color: var(--text-primary);
    font-size: 1.3rem;
    text-align: center;
}

.bar-chart {
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
}

.bar-item {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.bar-label {
    min-width: 120px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1.1rem;
}

.bar-track {
    flex: 1;
    height: 16px;
    background: var(--border-primary);
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    min-width: 300px;
}

.bar-fill {
    height: 100%;
    background: var(--orange-primary) !important;
    border-radius: 8px;
    width: 0%;
    transition: width 2s ease-in-out;
    position: relative;
}

.bar-fill.animate {
    width: var(--percentage);
}

.bar-value {
    min-width: 80px;
    text-align: right;
    font-weight: 700;
    color: var(--purple-primary);
    font-size: 1.1rem;
    font-size: 0.9rem;
}

/* Result Cards with Icons */
.result-card {
    position: relative;
    padding-top: 4rem;
    padding-bottom: 2rem;
}

.result-icon {
    position: absolute;
    top: -1.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 70px;
    background: var(--orange-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3);
    z-index: 10;
}

/* Growth Rate Section */
.growth-rate-section {
    padding: 5rem 0;
    background: #f5f5f5;
}

.growth-rate-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
}

.growth-rate-text {
    padding-right: 2rem;
}

.growth-rate-title {
    font-size: 4rem;
    font-weight: 800;
    color: #000;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.growth-rate-description {
    font-size: 1.2rem;
    color: #333;
    line-height: 1.6;
    max-width: 400px;
}

.growth-rate-chart {
    position: relative;
    background: white;
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.growth-rate-grid {
    display: flex;
    align-items: end;
    justify-content: space-between;
    gap: 2rem;
    height: 300px;
    position: relative;
}

.growth-rate-grid::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(to right, transparent 0%, transparent 100%),
        repeating-linear-gradient(
            to top,
            #f0f0f0 0px,
            #f0f0f0 1px,
            transparent 1px,
            transparent 60px
        );
    pointer-events: none;
}

.growth-rate-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 2;
}

.growth-bar {
    width: 60px;
    height: 200px;
    background: #8B5CF6;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    transform: scaleY(0);
    transform-origin: bottom;
    transition: transform 1.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.growth-bar.animate {
    transform: scaleY(1);
}

.growth-bar[data-height="20"] { height: 80px; }
.growth-bar[data-height="40"] { height: 120px; }
.growth-bar[data-height="60"] { height: 160px; }
.growth-bar[data-height="80"] { height: 200px; }
.growth-bar[data-height="100"] { height: 240px; }

.growth-value {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
}

.growth-label {
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
    margin-top: 0.5rem;
}

/* Marketing Tools Section */
.marketing-tools {
    padding: 5rem 0;
    background: var(--bg-primary);
}

.tools-showcase {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 4rem;
}

.tools-showcase-centered {
    grid-template-columns: 1fr;
    justify-items: center;
    text-align: center;
    max-width: 900px;
    margin: 0 auto 4rem auto;
    padding: 2rem;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.tools-showcase-centered .growth-metrics {
    width: 100%;
    max-width: 700px;
}

.tools-showcase-centered .growth-metrics h3 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.tools-showcase-centered .growth-metrics p {
    font-size: 1.1rem;
    line-height: 1.7;
    margin-bottom: 2.5rem;
    color: var(--text-secondary);
}

.tools-showcase-centered .growth-stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.tools-showcase-centered .growth-stat {
    background: var(--bg-light);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.tools-showcase-centered .growth-stat:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
    border-color: var(--purple-primary);
}

.tools-showcase-centered .feature-list {
    gap: 1.5rem;
    margin-top: 1rem;
}

.tools-showcase-centered .feature-item {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    transition: all 0.3s ease;
    justify-content: flex-start;
    text-align: left;
}

.tools-showcase-centered .feature-item:hover {
    background: var(--bg-light);
    border-color: var(--orange-primary);
    transform: translateX(10px);
}

.tool-visual {
    position: relative;
}

.tool-image {
    width: 100%;
    max-width: 500px;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.tool-image:hover {
    transform: scale(1.02);
}

.animated-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.pulse-dot {
    position: absolute;
    width: 12px;
    height: 12px;
    background: var(--orange-primary);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
}

.pulse-dot:nth-child(1) {
    animation-delay: 0s;
}

.pulse-dot:nth-child(2) {
    animation-delay: 0.7s;
}

.pulse-dot:nth-child(3) {
    animation-delay: 1.4s;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.7;
    }
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: var(--purple-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.feature-content h4 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.feature-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

.analytics-preview {
    text-align: center;
}

.analytics-image {
    width: 100%;
    max-width: 800px;
    height: auto;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    transition: var(--transition-normal);
}

.analytics-image:hover {
    transform: scale(1.02);
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
    .marketing-hero-image {
        margin-top: 2rem;
    }

    .hero-main-image {
        max-width: 100%;
    }

    .stat-bubble {
        position: relative;
        margin: 1rem auto;
        display: flex;
        justify-content: center;
        max-width: 200px;
    }

    .stat-bubble:nth-child(1),
    .stat-bubble:nth-child(2),
    .stat-bubble:nth-child(3) {
        position: relative;
        top: auto;
        left: auto;
        right: auto;
        bottom: auto;
    }

    .floating-stats {
        position: relative;
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
        pointer-events: auto;
    }

    .metrics-content,
    .results-content,
    .tools-showcase {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    /* Marketing Agenda Charts - Enhanced Mobile */
    .chart-grid {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem !important;
        padding: 0 0.5rem !important;
        justify-items: center !important;
        align-items: start !important;
    }

    .circular-chart {
        padding: 1rem !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
        width: 100% !important;
        max-width: 160px !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        background: var(--bg-card) !important;
        border: 1px solid var(--border-color) !important;
    }

    .circle-progress {
        width: 100px !important;
        height: 100px !important;
        position: relative !important;
    }

    .circle-svg {
        width: 100px !important;
        height: 100px !important;
        overflow: visible !important;
    }

    .circle-svg circle {
        stroke-width: 6 !important;
    }

    .circle-text {
        text-align: center !important;
    }

    .circle-text .percentage {
        font-size: 1.5rem !important;
        font-weight: 700 !important;
        color: var(--text-primary) !important;
        display: block !important;
        line-height: 1.2 !important;
    }

    .circle-text .label {
        font-size: 0.75rem !important;
        color: var(--text-secondary) !important;
        display: block !important;
        margin-top: 0.25rem !important;
        line-height: 1.2 !important;
        font-weight: 500 !important;
    }

    .circle-svg .circle-bg {
        stroke: #e5e7eb !important;
        stroke-width: 8 !important;
    }

    .circle-text {
        width: 120px !important;
        height: 120px !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
    }

    .circle-text .percentage {
        font-size: 1.3rem !important;
        font-weight: 800 !important;
        color: var(--purple-primary) !important;
        margin-bottom: 4px !important;
        line-height: 1 !important;
    }

    .circle-text .label {
        font-size: 0.7rem !important;
        max-width: 80px !important;
        line-height: 1.2 !important;
        text-align: center !important;
        color: var(--text-secondary) !important;
        font-weight: 600 !important;
    }

    /* Proven Results Bar Chart - Enhanced Mobile */
    .bar-chart-container {
        padding: 1.25rem !important;
        margin: 0 0.5rem !important;
        border-radius: 16px !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
    }

    .bar-chart-container h3 {
        font-size: 1.1rem !important;
        margin-bottom: 1.5rem !important;
        text-align: center !important;
        color: var(--text-primary) !important;
    }

    .bar-chart {
        gap: 1.25rem !important;
    }

    .bar-item {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 0.5rem !important;
        padding: 0.5rem 0 !important;
    }

    .bar-label {
        min-width: auto !important;
        font-size: 0.9rem !important;
        font-weight: 600 !important;
        color: var(--text-primary) !important;
        margin-bottom: 0.25rem !important;
    }

    .bar-track {
        width: 100% !important;
        height: 10px !important;
        background: var(--border-primary) !important;
        border-radius: 5px !important;
    }

    .bar-fill {
        height: 10px !important;
        border-radius: 5px !important;
        background: var(--orange-primary) !important;
    }

    .bar-value {
        min-width: auto !important;
        text-align: left !important;
        font-size: 1rem !important;
        font-weight: 700 !important;
        color: var(--purple-primary) !important;
        margin-top: 0.25rem !important;
    }

    .floating-metrics {
        position: relative;
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 1rem;
        pointer-events: auto;
    }

    .metric-card {
        position: relative;
        top: auto !important;
        left: auto !important;
        right: auto !important;
        bottom: auto !important;
    }

    .feature-item {
        padding: 1rem;
    }

    .feature-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }



    .growth-stats-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .tools-showcase-centered {
        padding: 1.5rem;
        margin: 0 auto 2rem auto;
    }

    .tools-showcase-centered .growth-metrics h3 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .tools-showcase-centered .growth-metrics p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .tools-showcase-centered .growth-stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .tools-showcase-centered .growth-stat {
        padding: 1rem;
    }

    .tools-showcase-centered .feature-item {
        padding: 1rem;
        text-align: center;
        justify-content: center;
    }

    .objectives-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem !important;
        margin-top: 2rem;
    }

    .objective-card {
        padding: 1.25rem !important;
        text-align: center;
    }

    .objective-icon {
        width: 40px !important;
        height: 40px !important;
        font-size: 0.9rem !important;
        margin: 0 auto 0.5rem auto !important;
        line-height: 1;
        text-align: center;
    }

    .objective-icon i {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        text-align: center;
        line-height: 1;
        font-weight: 900;
        position: relative;
    }

    /* Mobile specific icon adjustments */
    .objective-icon i.fa-chart-line {
        transform: translateX(-1px);
    }

    .objective-icon i.fa-bullseye {
        transform: translateX(0px);
    }

    .objective-icon i.fa-rocket {
        transform: translateX(1px);
    }

    .objective-title {
        font-size: 1.2rem;
        margin-bottom: 0.75rem;
        text-align: center;
    }

    .objective-description {
        font-size: 0.9rem;
        margin-bottom: 1rem;
        text-align: center;
    }

    .objective-features {
        align-items: center;
        text-align: left;
    }

    .objective-features li {
        font-size: 0.85rem;
        margin-bottom: 0.5rem;
        justify-content: flex-start;
    }

    .growth-stat {
        padding: 0.75rem;
    }

    .growth-stat .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .growth-stat .stat-number {
        font-size: 1.2rem;
    }

    .growth-stat .stat-label {
        font-size: 0.8rem;
    }

    /* Growth Rate Mobile */
    .growth-rate-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .growth-rate-item:nth-child(4),
    .growth-rate-item:nth-child(5) {
        grid-column: span 1;
    }

    .growth-rate-item:nth-child(5) {
        grid-column: 2;
    }

    .growth-number {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }

    .growth-bar {
        width: 40px;
        height: 120px;
    }

    .growth-value {
        font-size: 1.2rem;
    }

    .growth-label {
        font-size: 0.8rem;
    }

    .summary-content h3 {
        font-size: 1.5rem;
    }

    .summary-content p {
        font-size: 1rem;
    }

    .growth-highlights {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .highlight-item {
        padding: 0.5rem 1rem;
    }

    /* Meeting Agenda Mobile */
    .agenda-row {
        gap: 1.5rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .agenda-number {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    /* Growth Rate Mobile */
    .growth-rate-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .growth-rate-text {
        padding-right: 0;
    }

    .growth-rate-title {
        font-size: 2.5rem;
    }

    .growth-rate-grid {
        gap: 1rem;
        height: 200px;
    }

    .growth-bar {
        width: 40px;
    }

    .growth-bar[data-height="20"] { height: 50px; }
    .growth-bar[data-height="40"] { height: 80px; }
    .growth-bar[data-height="60"] { height: 110px; }
    .growth-bar[data-height="80"] { height: 140px; }
    .growth-bar[data-height="100"] { height: 170px; }

    .growth-value {
        font-size: 1rem;
        top: -30px;
    }

    .growth-label {
        font-size: 0.8rem;
    }
}

/* Extra Small Mobile Devices - Marketing Sections */
@media (max-width: 400px) {
    /* Marketing Agenda - Very Small Screens */
    .chart-grid {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 0.5rem !important;
        padding: 0 0.25rem !important;
        justify-items: center !important;
        align-items: start !important;
    }

    .circular-chart {
        padding: 0.75rem !important;
        margin: 0 auto !important;
        max-width: 140px !important;
        width: 100% !important;
        border-radius: 10px !important;
    }

    .circle-progress {
        width: 80px !important;
        height: 80px !important;
    }

    .circle-svg {
        width: 80px !important;
        height: 80px !important;
        overflow: visible !important;
    }

    .circle-svg circle {
        stroke-width: 6 !important;
    }

    .circle-svg .circle-bg {
        stroke: #e5e7eb !important;
        stroke-width: 6 !important;
    }

    .circle-text {
        width: 80px !important;
        height: 80px !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
    }

    .circle-text .percentage {
        font-size: 1.1rem !important;
        font-weight: 800 !important;
        line-height: 1 !important;
    }

    .circle-text .label {
        font-size: 0.6rem !important;
        max-width: 70px !important;
        line-height: 1.1 !important;
    }

    /* Bar Chart - Very Small Screens */
    .bar-chart-container {
        padding: 1rem !important;
        margin: 0 !important;
    }

    .bar-chart-container h3 {
        font-size: 1rem !important;
        margin-bottom: 1rem !important;
    }

    .bar-chart {
        gap: 1rem !important;
    }

    .bar-label {
        font-size: 0.85rem !important;
    }

    .bar-track {
        height: 8px !important;
    }

    .bar-fill {
        height: 8px !important;
    }

    .bar-value {
        font-size: 0.9rem !important;
    }

    /* Results Grid - Very Small Screens */
    .results-grid {
        gap: 0.75rem !important;
        padding: 0 0.5rem !important;
        grid-template-columns: repeat(2, 1fr) !important;
        max-width: 100% !important;
    }

    .result-card {
        padding: 0.5rem !important;
        padding-top: 2rem !important;
        text-align: center !important;
        min-height: auto !important;
    }

    .result-icon {
        width: 35px !important;
        height: 35px !important;
        font-size: 0.9rem !important;
        top: -0.75rem !important;
    }

    .result-number {
        font-size: 1.25rem !important;
        margin-top: 0.25rem !important;
        margin-bottom: 0.15rem !important;
        font-weight: 700 !important;
    }

    .result-label {
        font-size: 0.65rem !important;
        margin-bottom: 0.15rem !important;
        line-height: 1.1 !important;
        font-weight: 600 !important;
    }

    .result-card p {
        font-size: 0.6rem !important;
        line-height: 1.2 !important;
        margin: 0 !important;
        opacity: 0.9 !important;
    }

    .result-number {
        font-size: 1.8rem !important;
        margin-bottom: 0.5rem !important;
    }

    .result-label {
        font-size: 0.85rem !important;
        margin-bottom: 0.75rem !important;
    }

    .result-card p {
        font-size: 0.8rem !important;
        line-height: 1.4 !important;
    }
}
