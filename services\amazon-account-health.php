<?php
// Page configuration
$page_title = "Amazon Account Health Management | Shiftur";
$page_description = "Professional Amazon account health monitoring and management services. Maintain optimal performance, resolve issues, and protect your selling privileges.";

// Base path for navigation
$base_path = '../';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
    
    <!-- CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- AOS Animation -->
    <link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        /* Make customer loyalty banner image bigger */
        .content-section .content-visual img[alt="Customer Loyalty Banner"] {
            width: 100%;
            max-width: 800px;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        /* Ensure the container is also larger */
        .content-section .content-visual {
            max-width: 900px;
            margin: 0 auto;
        }

        /* Fix content-features section styling */
        .content-features {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            margin-top: 2rem;
        }

        .content-features .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.5rem;
            background: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-normal);
        }

        .content-features .feature-item:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-2px);
        }

        .content-features .feature-item i {
            font-size: 1.5rem;
            margin-top: 0.25rem;
            flex-shrink: 0;
        }

        .content-features .feature-item div {
            flex: 1;
        }

        .content-features .feature-item h4 {
            margin: 0 0 0.5rem 0;
            color: var(--text-primary);
            font-size: 1.1rem;
            font-weight: 600;
        }

        .content-features .feature-item p {
            margin: 0;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* Make trusted service provider image bigger */
        .content-visual img[alt="Trusted Service Provider"] {
            width: 100%;
            max-width: 700px;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        /* Make Account Health Dashboard image bigger */
        .content-visual img[alt="Account Health Dashboard"] {
            width: 100%;
            max-width: 750px;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        /* Fix content-list section styling */
        .content-list {
            list-style: none;
            padding: 0;
            margin: 2rem 0;
            display: flex;
            flex-direction: column;
            gap: 1.25rem;
        }

        .content-list li {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.25rem;
            background: var(--bg-card);
            border: 1px solid var(--border-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-normal);
        }

        .content-list li:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        .content-list li i {
            font-size: 1.2rem;
            margin-top: 0.2rem;
            flex-shrink: 0;
        }

        .content-list li strong {
            color: var(--text-primary);
            font-weight: 600;
            display: block;
            margin-bottom: 0.25rem;
        }

        .content-list li {
            color: var(--text-secondary);
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <?php include '../includes/header.php'; ?>

    <!-- Hero Section -->
    <section class="service-hero">
        <div class="container">
            <div class="service-hero-content">
                <div class="hero-text">
                    <h1 class="hero-title" data-aos="fade-up">
                        Amazon Account Health <span class="text-purple">Management</span>
                    </h1>
                    <p class="hero-description" data-aos="fade-up" data-aos-delay="100">
                        Protect your Amazon selling privileges with our comprehensive account health monitoring and management services. Stay compliant, resolve issues quickly, and maintain optimal performance.
                    </p>
                    <div class="hero-actions" data-aos="fade-up" data-aos-delay="200">
                        <a href="../contact.php" class="btn btn-primary">Get Account Health Audit</a>
                        <a href="#health-monitoring" class="btn btn-outline">Learn More</a>
                    </div>
                </div>
                <div class="hero-visual" data-aos="fade-left" data-aos-delay="300">
                    <img src="../assets/images/amazon/WhatsApp Image 2025-07-24 at 9.26.13 PM.jpeg" alt="Amazon Account Health Management" class="hero-image">
                </div>
            </div>
        </div>
    </section>
<br><br>
    <!-- Account Health Monitoring Section -->
    <section id="health-monitoring" class="content-section">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">Comprehensive Account Health Monitoring</h2>
                <p class="section-description">
                    Your Amazon account health directly impacts your ability to sell and grow on the platform. Our proactive monitoring ensures you stay ahead of potential issues.
                </p>
            </div>
            
            <div class="content-grid">
                <div class="content-text" data-aos="fade-right">
                    <h3>Performance Metrics Tracking</h3>
                    <p>We continuously monitor all critical performance metrics that affect your account health, including order defect rate, late shipment rate, and customer feedback scores.</p>
                    
                    <div class="content-features">
                        <div class="feature-item">
                            <i class="fas fa-chart-line text-purple"></i>
                            <div>
                                <h4>Real-time Monitoring</h4>
                                <p>24/7 tracking of key performance indicators and instant alerts for any issues</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-shield-alt text-purple"></i>
                            <div>
                                <h4>Policy Compliance</h4>
                                <p>Ensure adherence to Amazon's policies and guidelines to prevent violations</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-exclamation-triangle text-purple"></i>
                            <div>
                                <h4>Issue Prevention</h4>
                                <p>Proactive measures to prevent account health issues before they occur</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="content-visual" data-aos="fade-left">
                    <img src="../assets/images/amazon/WhatsApp Image 2025-07-24 at 9.28.12 PM.jpeg" alt="Account Health Dashboard" class="content-image">
                </div>
            </div>
        </div>
    </section>
<br><br>
    <!-- Key Metrics Section -->
    <section class="content-section bg-secondary">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">Key Metrics We Monitor</h2>
                <p class="section-description">
                    We track all essential metrics that Amazon uses to evaluate seller performance and account health.
                </p>
            </div>
            
            <div class="benefits-grid">
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="100">
                    <div class="benefit-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <h4>Order Defect Rate (ODR)</h4>
                    <p>Monitor and maintain ODR below 1% to ensure good standing with Amazon's performance standards.</p>
                </div>
                
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="200">
                    <div class="benefit-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4>Late Shipment Rate</h4>
                    <p>Track shipping performance to maintain fast delivery times and customer satisfaction.</p>
                </div>
                
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="300">
                    <div class="benefit-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h4>Customer Feedback</h4>
                    <p>Monitor customer reviews and feedback to maintain high seller ratings and reputation.</p>
                </div>
                
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="400">
                    <div class="benefit-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                    <h4>Return Dissatisfaction Rate</h4>
                    <p>Track return-related issues to ensure smooth return processes and customer satisfaction.</p>
                </div>
                
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="500">
                    <div class="benefit-icon">
                        <i class="fas fa-gavel"></i>
                    </div>
                    <h4>Policy Violations</h4>
                    <p>Monitor for any policy violations and take immediate corrective action when needed.</p>
                </div>
                
                <div class="benefit-card" data-aos="fade-up" data-aos-delay="600">
                    <div class="benefit-icon">
                        <i class="fas fa-ban"></i>
                    </div>
                    <h4>Account Restrictions</h4>
                    <p>Track any account restrictions or warnings and work to resolve them quickly.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Issue Resolution Section -->
    <section class="content-section">
        <div class="container">
            <div class="content-grid">
                <div class="content-visual" data-aos="fade-right">
                    <img src="../assets/images/amazon/WhatsApp Image 2025-07-24 at 9.35.45 PM.jpeg" alt="Trusted Service Provider" class="content-image">
                </div>
                <div class="content-text" data-aos="fade-left">
                    <h3>Rapid Issue Resolution</h3>
                    <p>When account health issues arise, quick action is essential. Our experienced team knows how to address problems efficiently and effectively.</p>
                    
                    <ul class="content-list">
                        <li><i class="fas fa-check text-purple"></i> <strong>Immediate Response:</strong> Quick identification and assessment of account health issues</li>
                        <li><i class="fas fa-check text-purple"></i> <strong>Expert Communication:</strong> Professional correspondence with Amazon Seller Support</li>
                        <li><i class="fas fa-check text-purple"></i> <strong>Documentation:</strong> Proper documentation and evidence gathering for appeals</li>
                        <li><i class="fas fa-check text-purple"></i> <strong>Action Plans:</strong> Detailed plans to prevent future occurrences</li>
                        <li><i class="fas fa-check text-purple"></i> <strong>Follow-up:</strong> Continuous monitoring to ensure issues are fully resolved</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Preventive Measures Section -->
    <section class="content-section bg-secondary">
        <div class="container">
            <div class="section-header" data-aos="fade-up">
                <h2 class="section-title">Preventive Measures</h2>
                <p class="section-description">
                    Prevention is better than cure. We implement proactive strategies to maintain excellent account health.
                </p>
            </div>
            
            <div class="process-steps">
                <div class="process-step" data-aos="fade-up" data-aos-delay="100">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Regular Audits</h4>
                        <p>Comprehensive account health audits to identify potential risks and areas for improvement.</p>
                    </div>
                </div>
                
                <div class="process-step" data-aos="fade-up" data-aos-delay="200">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>Policy Updates</h4>
                        <p>Stay current with Amazon's evolving policies and ensure your account remains compliant.</p>
                    </div>
                </div>
                
                <div class="process-step" data-aos="fade-up" data-aos-delay="300">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Best Practices</h4>
                        <p>Implement industry best practices for inventory management, customer service, and operations.</p>
                    </div>
                </div>
                
                <div class="process-step" data-aos="fade-up" data-aos-delay="400">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>Training & Support</h4>
                        <p>Ongoing training and support to help your team maintain excellent account health standards.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Customer Loyalty Section -->
    <section class="content-section">
        <div class="container">
            <div class="content-grid content-centered" data-aos="fade-up">
                <div class="content-visual">
                    <img src="../assets/images/amazon/WhatsApp Image 2025-07-24 at 9.27.06 PM.jpeg" alt="Customer Loyalty Banner" class="content-image">
                </div>
            </div>
        </div>
    </section>

    <?php include '../includes/footer.php'; ?>

    <!-- JavaScript -->
    <script src="../assets/js/main.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
