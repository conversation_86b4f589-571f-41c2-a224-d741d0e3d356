<?php
session_start();
require_once '../includes/admin-auth.php';

// Check if admin user is logged in
requireAdminLogin();

// Get admin user data
$admin_user = getAdminUser($_SESSION['admin_user_id']);

if (!$admin_user) {
    logoutAdminUser();
    header('Location: login.php');
    exit;
}

$success_message = '';
$error_message = '';

// Handle message status updates
if ($_POST && isset($_POST['update_status'])) {
    $message_id = (int)($_POST['message_id'] ?? 0);
    $new_status = sanitizeInput($_POST['status'] ?? '');

    if ($message_id && in_array($new_status, ['new', 'read', 'replied', 'archived'])) {
        $result = updateRecord($conn, 'contact_messages', ['status' => $new_status], ['id' => $message_id]);
        if ($result) {
            $success_message = 'Message status updated successfully.';
        } else {
            $error_message = 'Failed to update message status.';
        }
    }
}

// Get contact messages if table exists
$messages = [];
$message_stats = ['new' => 0, 'read' => 0, 'replied' => 0, 'archived' => 0];

try {
    $messages_result = $conn->query("SHOW TABLES LIKE 'contact_messages'");
    if ($messages_result && $messages_result->num_rows > 0) {
        $messages = fetchAll($conn, "SELECT * FROM contact_messages ORDER BY created_at DESC");

        // Calculate stats
        foreach ($messages as $message) {
            $status = $message['status'] ?? 'new';
            if (isset($message_stats[$status])) {
                $message_stats[$status]++;
            }
        }
    }
} catch (Exception $e) {
    $messages = [];
}

$page_title = 'Messages - Admin - Shiftur';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #8b5cf6, #6366f1);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .admin-nav {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .admin-nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            margin-bottom: 2rem;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            color: #6b7280;
        }
        
        .messages-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
        }
        
        .message-item {
            padding: 1.5rem;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.3s ease;
        }
        
        .message-item:last-child {
            border-bottom: none;
        }
        
        .message-item:hover {
            background: #f9fafb;
        }
        
        .message-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 1rem;
            gap: 1rem;
        }
        
        .message-info {
            flex: 1;
        }
        
        .message-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }
        
        .message-email {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 0.25rem;
        }
        
        .message-date {
            color: #9ca3af;
            font-size: 0.8rem;
        }
        
        .message-status {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-new {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-read {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-replied {
            background: #ddd6fe;
            color: #5b21b6;
        }
        
        .message-subject {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .message-content {
            color: #6b7280;
            line-height: 1.6;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: #6b7280;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        .empty-state h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            color: #374151;
        }
        
        .setup-info {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .setup-info h3 {
            color: #92400e;
            margin-bottom: 0.5rem;
        }
        
        .setup-info p {
            color: #78350f;
            margin-bottom: 0.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #6b7280;
            font-size: 0.9rem;
        }

        .stat-new .stat-number { color: #f59e0b; }
        .stat-read .stat-number { color: #10b981; }
        .stat-replied .stat-number { color: #8b5cf6; }
        .stat-archived .stat-number { color: #6b7280; }

        .message-actions {
            display: flex;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .btn-small {
            padding: 0.25rem 0.75rem;
            font-size: 0.8rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .btn-read { background: #10b981; color: white; }
        .btn-reply { background: #8b5cf6; color: white; }
        .btn-archive { background: #6b7280; color: white; }

        .btn-small:hover {
            opacity: 0.8;
        }

        .message-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 8px;
        }

        .meta-item {
            display: flex;
            flex-direction: column;
        }

        .meta-label {
            font-size: 0.8rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }

        .meta-value {
            font-weight: 500;
            color: #1f2937;
        }

        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            color: #16a34a;
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            color: #dc2626;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .admin-nav {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .message-header {
                flex-direction: column;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="header-content">
            <div class="admin-logo">
                <i class="fas fa-cog"></i> Shiftur Admin
            </div>
            
            <nav class="admin-nav">
                <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="portfolio.php"><i class="fas fa-folder-open"></i> Portfolio</a>
                <a href="users.php"><i class="fas fa-users"></i> Users</a>
                <a href="messages.php" class="active"><i class="fas fa-envelope"></i> Messages</a>
                <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
            </nav>
            
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_user['first_name'], 0, 1)); ?>
                </div>
                <div>
                    <div><?php echo htmlspecialchars($admin_user['first_name'] . ' ' . $admin_user['last_name']); ?></div>
                    <div style="font-size: 0.8rem; opacity: 0.8;"><?php echo htmlspecialchars($admin_user['role']); ?></div>
                </div>
                <a href="logout.php" style="color: white; margin-left: 1rem;">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="page-header">
            <h1 class="page-title">Messages</h1>
            <p class="page-subtitle">Contact form submissions and inquiries</p>
        </div>

        <!-- Flash Messages -->
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($success_message); ?>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error_message); ?>
            </div>
        <?php endif; ?>

        <!-- Message Statistics -->
        <?php if (!empty($messages)): ?>
            <div class="stats-grid">
                <div class="stat-card stat-new">
                    <div class="stat-number"><?php echo $message_stats['new']; ?></div>
                    <div class="stat-label">New Messages</div>
                </div>
                <div class="stat-card stat-read">
                    <div class="stat-number"><?php echo $message_stats['read']; ?></div>
                    <div class="stat-label">Read Messages</div>
                </div>
                <div class="stat-card stat-replied">
                    <div class="stat-number"><?php echo $message_stats['replied']; ?></div>
                    <div class="stat-label">Replied</div>
                </div>
                <div class="stat-card stat-archived">
                    <div class="stat-number"><?php echo $message_stats['archived']; ?></div>
                    <div class="stat-label">Archived</div>
                </div>
            </div>
        <?php endif; ?>

        <?php if (empty($messages)): ?>
            <div class="setup-info">
                <h3><i class="fas fa-info-circle"></i> Contact Messages Setup</h3>
                <p>To receive contact messages, you need to set up the contact_messages table in your database.</p>
                <p>Contact messages will appear here once users submit the contact form on your website.</p>
            </div>
        <?php endif; ?>
        
        <div class="messages-container">
            <?php if (!empty($messages)): ?>
                <?php foreach ($messages as $message): ?>
                    <div class="message-item">
                        <div class="message-header">
                            <div class="message-info">
                                <div class="message-name"><?php echo htmlspecialchars($message['name'] ?? 'Unknown'); ?></div>
                                <div class="message-email"><?php echo htmlspecialchars($message['email'] ?? 'No email'); ?></div>
                                <div class="message-date"><?php echo date('M d, Y g:i A', strtotime($message['created_at'])); ?></div>
                            </div>
                            <div class="message-status status-<?php echo $message['status'] ?? 'new'; ?>">
                                <?php echo ucfirst($message['status'] ?? 'new'); ?>
                            </div>
                        </div>

                        <?php if (isset($message['subject'])): ?>
                            <div class="message-subject"><?php echo htmlspecialchars($message['subject']); ?></div>
                        <?php endif; ?>

                        <!-- Message Details -->
                        <div class="message-meta">
                            <?php if (!empty($message['phone'])): ?>
                                <div class="meta-item">
                                    <div class="meta-label">Phone</div>
                                    <div class="meta-value"><?php echo htmlspecialchars($message['phone']); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($message['company'])): ?>
                                <div class="meta-item">
                                    <div class="meta-label">Company</div>
                                    <div class="meta-value"><?php echo htmlspecialchars($message['company']); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($message['service_interest'])): ?>
                                <div class="meta-item">
                                    <div class="meta-label">Service Interest</div>
                                    <div class="meta-value"><?php echo htmlspecialchars($message['service_interest']); ?></div>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($message['business_type'])): ?>
                                <div class="meta-item">
                                    <div class="meta-label">Business Type</div>
                                    <div class="meta-value"><?php echo htmlspecialchars($message['business_type']); ?></div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="message-content">
                            <?php echo nl2br(htmlspecialchars($message['message'] ?? $message['content'] ?? 'No message content')); ?>
                        </div>

                        <!-- Message Actions -->
                        <div class="message-actions">
                            <?php if ($message['status'] !== 'read'): ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">
                                    <input type="hidden" name="status" value="read">
                                    <button type="submit" name="update_status" class="btn-small btn-read">
                                        <i class="fas fa-eye"></i> Mark as Read
                                    </button>
                                </form>
                            <?php endif; ?>

                            <?php if ($message['status'] !== 'replied'): ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">
                                    <input type="hidden" name="status" value="replied">
                                    <button type="submit" name="update_status" class="btn-small btn-reply">
                                        <i class="fas fa-reply"></i> Mark as Replied
                                    </button>
                                </form>
                            <?php endif; ?>

                            <?php if ($message['status'] !== 'archived'): ?>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="message_id" value="<?php echo $message['id']; ?>">
                                    <input type="hidden" name="status" value="archived">
                                    <button type="submit" name="update_status" class="btn-small btn-archive">
                                        <i class="fas fa-archive"></i> Archive
                                    </button>
                                </form>
                            <?php endif; ?>

                            <a href="mailto:<?php echo htmlspecialchars($message['email']); ?>?subject=Re: <?php echo htmlspecialchars($message['subject']); ?>"
                               class="btn-small btn-reply">
                                <i class="fas fa-envelope"></i> Reply via Email
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="empty-state">
                    <i class="fas fa-envelope-open"></i>
                    <h3>No Messages Yet</h3>
                    <p>Contact form submissions will appear here when users send messages through your website.</p>
                </div>
            <?php endif; ?>
        </div>
    </main>
</body>
</html>
