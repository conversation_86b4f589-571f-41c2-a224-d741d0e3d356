<?php
session_start();
require_once '../includes/admin-auth.php';

// Check if admin user is logged in
requireAdminLogin();

// Get admin user data
$admin_user = getAdminUser($_SESSION['admin_user_id']);

if (!$admin_user) {
    logoutAdminUser();
    header('Location: login.php');
    exit;
}

// Handle form submissions
$success = '';
$error = '';

// Create portfolio table if it doesn't exist
try {
    // Create new portfolio table with simplified structure (only if it doesn't exist)
    $create_table_sql = "CREATE TABLE IF NOT EXISTS portfolio (
        id INT AUTO_INCREMENT PRIMARY KEY,
        category ENUM('web', 'app', 'graphic', 'ai', 'marketing') NOT NULL,
        image_path VARCHAR(500) NOT NULL,
        status ENUM('active', 'inactive') DEFAULT 'active',
        display_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    if (!$conn->query($create_table_sql)) {
        throw new Exception("Failed to create portfolio table: " . $conn->error);
    }
} catch (Exception $e) {
    $error = "Database error: " . $e->getMessage();
}

// Handle Add/Edit Portfolio
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'add' || $action === 'edit') {
            $category = $_POST['category'];
            $status = $_POST['status'];
            $display_order = intval($_POST['display_order']);

            $image_path = '';
            if ($action === 'edit' && !empty($_POST['existing_image']) && !isset($_POST['remove_image'])) {
                $image_path = $_POST['existing_image'];
            }

            // Process uploaded file
            if (!empty($_FILES['portfolio_image']['name'])) {
                $upload_dir = '../assets/images/portfolio/';
                if (!is_dir($upload_dir)) {
                    mkdir($upload_dir, 0755, true);
                }

                $file_name = $_FILES['portfolio_image']['name'];
                $file_tmp = $_FILES['portfolio_image']['tmp_name'];
                $file_ext = strtolower(pathinfo($file_name, PATHINFO_EXTENSION));

                // Validate file type
                $allowed_types = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                if (in_array($file_ext, $allowed_types)) {
                    $new_filename = uniqid() . '_' . time() . '.' . $file_ext;
                    $upload_path = $upload_dir . $new_filename;

                    if (move_uploaded_file($file_tmp, $upload_path)) {
                        // Delete old image if editing
                        if ($action === 'edit' && $image_path && file_exists('../' . $image_path)) {
                            unlink('../' . $image_path);
                        }
                        $image_path = 'assets/images/portfolio/' . $new_filename;
                    }
                } else {
                    $error = "Invalid file type. Please upload JPG, PNG, GIF, or WebP images only.";
                }
            }

            if (!$error) {
                if ($action === 'add') {
                    if (empty($image_path)) {
                        $error = "Please upload a portfolio image.";
                    } else {
                        $stmt = $conn->prepare("INSERT INTO portfolio (category, image_path, status, display_order) VALUES (?, ?, ?, ?)");
                        if ($stmt) {
                            $stmt->bind_param("sssi", $category, $image_path, $status, $display_order);

                            if ($stmt->execute()) {
                                $success = "Portfolio item added successfully!";
                            } else {
                                $error = "Error adding portfolio item: " . $stmt->error;
                            }
                            $stmt->close();
                        } else {
                            $error = "Database prepare error: " . $conn->error;
                        }
                    }
                } else {
                    $id = intval($_POST['id']);
                    if (empty($image_path) && !isset($_POST['remove_image'])) {
                        $error = "Please upload a portfolio image or keep the existing one.";
                    } else {
                        $stmt = $conn->prepare("UPDATE portfolio SET category=?, image_path=?, status=?, display_order=? WHERE id=?");
                        if ($stmt) {
                            $stmt->bind_param("sssii", $category, $image_path, $status, $display_order, $id);

                            if ($stmt->execute()) {
                                $success = "Portfolio item updated successfully!";
                            } else {
                                $error = "Error updating portfolio item: " . $stmt->error;
                            }
                            $stmt->close();
                        } else {
                            $error = "Database prepare error: " . $conn->error;
                        }
                    }
                }
            }
        } elseif ($action === 'delete') {
            $id = intval($_POST['id']);
            
            // Get portfolio item to delete associated image
            $stmt = $conn->prepare("SELECT image_path FROM portfolio WHERE id = ?");
            if ($stmt) {
                $stmt->bind_param("i", $id);
                $stmt->execute();
                $result = $stmt->get_result();
                $portfolio = $result->fetch_assoc();
                $stmt->close();

                if ($portfolio) {
                    // Delete associated image file
                    $image_path = '../' . $portfolio['image_path'];
                    if (file_exists($image_path)) {
                        unlink($image_path);
                    }

                    // Delete from database
                    $stmt = $conn->prepare("DELETE FROM portfolio WHERE id = ?");
                    if ($stmt) {
                        $stmt->bind_param("i", $id);

                        if ($stmt->execute()) {
                            $success = "Portfolio item deleted successfully!";
                        } else {
                            $error = "Error deleting portfolio item: " . $stmt->error;
                        }
                        $stmt->close();
                    } else {
                        $error = "Database prepare error: " . $conn->error;
                    }
                } else {
                    $error = "Portfolio item not found.";
                }
            } else {
                $error = "Database prepare error: " . $conn->error;
            }
        }
    }
}

// Get all portfolio items
$portfolio_items = [];
try {
    $result = $conn->query("SELECT * FROM portfolio ORDER BY display_order ASC, created_at DESC");
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $portfolio_items[] = $row;
        }
    }
} catch (Exception $e) {
    // Table might not exist yet, ignore error
}

// Get portfolio item for editing
$edit_item = null;
if (isset($_GET['edit'])) {
    $edit_id = intval($_GET['edit']);
    $stmt = $conn->prepare("SELECT * FROM portfolio WHERE id = ?");
    if ($stmt) {
        $stmt->bind_param("i", $edit_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $edit_item = $result->fetch_assoc();
        $stmt->close();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Management - Admin Panel</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #8b5cf6, #6366f1);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .admin-nav {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .admin-nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .page-header {
            margin-bottom: 2rem;
        }
        
        .page-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            color: #6b7280;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .portfolio-form {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            margin-bottom: 2rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #374151;
        }
        
        .form-input,
        .form-select,
        .form-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }
        
        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: #8b5cf6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #7c3aed;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-danger {
            background: #dc2626;
            color: white;
        }
        
        .btn-danger:hover {
            background: #b91c1c;
        }
        
        .portfolio-list {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }
        
        .portfolio-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .portfolio-table th,
        .portfolio-table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .portfolio-table th {
            background: #f9fafb;
            font-weight: 600;
            color: #374151;
        }
        
        .portfolio-table tr:hover {
            background: #f9fafb;
        }
        
        .portfolio-type-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .type-image {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .type-website {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-active {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .portfolio-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .portfolio-preview {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .preview-image {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #e5e7eb;
        }
        
        .portfolio-type-fields {
            display: none;
        }
        
        .portfolio-type-fields.active {
            display: block;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .main-content {
                padding: 1rem;
            }
            
            .portfolio-table {
                font-size: 0.875rem;
            }
            
            .portfolio-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="header-content">
            <div class="admin-logo">
                <i class="fas fa-cog"></i> Admin Panel
            </div>
            <nav class="admin-nav">
                <a href="dashboard.php">Dashboard</a>
                <a href="portfolio.php" class="active">Portfolio</a>
                <a href="messages.php">Messages</a>
                <a href="users.php">Users</a>
                <a href="settings.php">Settings</a>
            </nav>
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_user['username'], 0, 2)); ?>
                </div>
                <span><?php echo htmlspecialchars($admin_user['username']); ?></span>
                <a href="logout.php" class="btn btn-secondary">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>
    </header>

    <div class="main-content">
        <div class="page-header">
            <h1 class="page-title">Portfolio Management</h1>
            <p class="page-subtitle">Manage your portfolio items - graphic designs and website showcases</p>
        </div>

        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <!-- Portfolio Form -->
        <div class="portfolio-form">
            <h2><?php echo $edit_item ? 'Edit Portfolio Item' : 'Add New Portfolio Item'; ?></h2>

            <form method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" value="<?php echo $edit_item ? 'edit' : 'add'; ?>">
                <?php if ($edit_item): ?>
                    <input type="hidden" name="id" value="<?php echo $edit_item['id']; ?>">
                    <input type="hidden" name="existing_image" value="<?php echo htmlspecialchars($edit_item['image_path']); ?>">
                <?php endif; ?>

                <div class="form-group">
                    <label class="form-label">Category *</label>
                    <select name="category" class="form-select" required>
                        <option value="">Select Category</option>
                        <option value="web" <?php echo ($edit_item && $edit_item['category'] === 'web') ? 'selected' : ''; ?>>Web Development</option>
                        <option value="app" <?php echo ($edit_item && $edit_item['category'] === 'app') ? 'selected' : ''; ?>>App Development</option>
                        <option value="graphic" <?php echo ($edit_item && $edit_item['category'] === 'graphic') ? 'selected' : ''; ?>>Graphic Design</option>
                        <option value="ai" <?php echo ($edit_item && $edit_item['category'] === 'ai') ? 'selected' : ''; ?>>AI Solutions</option>
                        <option value="marketing" <?php echo ($edit_item && $edit_item['category'] === 'marketing') ? 'selected' : ''; ?>>Digital Marketing</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">Portfolio Image *</label>
                    <input type="file" name="portfolio_image" class="form-input" accept="image/*" <?php echo $edit_item ? '' : 'required'; ?>>
                    <small style="color: #6b7280;">Upload a portfolio image (JPG, PNG, GIF, WebP)</small>

                    <?php if ($edit_item && $edit_item['image_path']): ?>
                        <div style="margin-top: 1rem;">
                            <img src="../<?php echo htmlspecialchars($edit_item['image_path']); ?>"
                                 style="max-width: 200px; height: auto; border-radius: 8px; border: 1px solid #e5e7eb;"
                                 alt="Current Portfolio Image">
                            <p style="margin-top: 0.5rem; color: #6b7280; font-size: 0.875rem;">Current image (upload new to replace)</p>

                            <div style="margin-top: 1rem;">
                                <label style="display: flex; align-items: center; gap: 0.5rem; color: #dc2626; cursor: pointer;">
                                    <input type="checkbox" name="remove_image" value="1" style="margin: 0;">
                                    <span>Remove current image</span>
                                </label>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">Display Order</label>
                        <input type="number" name="display_order" class="form-input" min="0"
                               value="<?php echo $edit_item ? $edit_item['display_order'] : '0'; ?>">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="active" <?php echo ($edit_item && $edit_item['status'] === 'active') ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo ($edit_item && $edit_item['status'] === 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                </div>

                <div style="margin-top: 2rem;">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        <?php echo $edit_item ? 'Update Portfolio Item' : 'Add Portfolio Item'; ?>
                    </button>

                    <?php if ($edit_item): ?>
                        <a href="portfolio.php" class="btn btn-secondary">
                            <i class="fas fa-times"></i>
                            Cancel
                        </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <!-- Portfolio List -->
        <div class="portfolio-list">
            <h2 style="padding: 1.5rem; margin: 0; border-bottom: 1px solid #e5e7eb;">Portfolio Items</h2>

            <?php if (empty($portfolio_items)): ?>
                <div style="padding: 2rem; text-align: center; color: #6b7280;">
                    <i class="fas fa-folder-open" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;"></i>
                    <p>No portfolio items found. Add your first portfolio item above.</p>
                </div>
            <?php else: ?>
                <table class="portfolio-table">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Image Preview</th>
                            <th>Status</th>
                            <th>Order</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($portfolio_items as $item): ?>
                            <tr>
                                <td>
                                    <span style="text-transform: capitalize;">
                                        <?php echo htmlspecialchars($item['category']); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($item['image_path']): ?>
                                        <img src="../<?php echo htmlspecialchars($item['image_path']); ?>" class="preview-image" alt="Portfolio">
                                    <?php else: ?>
                                        <span style="color: #6b7280;">No image</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="status-badge <?php echo $item['status'] === 'active' ? 'status-active' : 'status-inactive'; ?>">
                                        <?php echo ucfirst($item['status']); ?>
                                    </span>
                                </td>
                                <td><?php echo $item['display_order']; ?></td>
                                <td>
                                    <div class="portfolio-actions">
                                        <a href="portfolio.php?edit=<?php echo $item['id']; ?>" class="btn btn-secondary" style="padding: 0.5rem;">
                                            <i class="fas fa-edit"></i>
                                        </a>

                                        <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this portfolio item?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="id" value="<?php echo $item['id']; ?>">
                                            <button type="submit" class="btn btn-danger" style="padding: 0.5rem;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>

    <script>
        // Auto-resize textareas
        document.querySelectorAll('.form-textarea').forEach(textarea => {
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });
        });
    </script>
</body>
</html>
