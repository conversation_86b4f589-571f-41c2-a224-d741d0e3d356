<?php
// Admin authentication system
require_once 'db.php';

// Create admin user
function createAdminUser($data) {
    global $conn;
    if (!$conn) return ['success' => false, 'message' => 'Database connection failed'];
    
    // Validate required fields
    $required = ['username', 'email', 'password', 'first_name', 'last_name'];
    foreach ($required as $field) {
        if (empty($data[$field])) {
            return ['success' => false, 'message' => "Field '$field' is required"];
        }
    }
    
    // Check if username or email already exists
    $existing = fetchOne($conn, "SELECT id FROM admin_users WHERE username = ? OR email = ?", [$data['username'], $data['email']]);
    if ($existing) {
        return ['success' => false, 'message' => 'Username or email already exists'];
    }
    
    // Hash password
    $password_hash = hashPassword($data['password']);
    
    // Insert admin user
    $insert_data = [
        'username' => sanitizeInput($data['username']),
        'email' => sanitizeInput($data['email']),
        'password' => $password_hash,
        'first_name' => sanitizeInput($data['first_name']),
        'last_name' => sanitizeInput($data['last_name']),
        'role' => $data['role'] ?? 'admin',
        'status' => $data['status'] ?? 'active'
    ];
    
    $user_id = insertRecord($conn, 'admin_users', $insert_data);
    
    if ($user_id) {
        return ['success' => true, 'user_id' => $user_id, 'message' => 'Admin user created successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to create admin user'];
    }
}

// Authenticate admin user
function authenticateAdminUser($username, $password) {
    global $conn;
    if (!$conn) return false;
    
    $user = fetchOne($conn, "SELECT * FROM admin_users WHERE (username = ? OR email = ?) AND status = 'active'", [$username, $username]);
    
    if ($user && verifyPassword($password, $user['password'])) {
        return $user;
    }
    
    return false;
}

// Login admin user
function loginAdminUser($user) {
    $_SESSION['admin_user_id'] = $user['id'];
    $_SESSION['admin_username'] = $user['username'];
    $_SESSION['admin_user_email'] = $user['email'];
    $_SESSION['admin_user_name'] = $user['first_name'] . ' ' . $user['last_name'];
    $_SESSION['admin_user_role'] = $user['role'];
    $_SESSION['admin_user_status'] = $user['status'];
    $_SESSION['admin_login_time'] = time();
    
    // Update last login
    updateRecord($GLOBALS['conn'], 'admin_users', ['last_login' => date('Y-m-d H:i:s')], ['id' => $user['id']]);
    
    return true;
}

// Logout admin user
function logoutAdminUser() {
    // Clear admin session variables
    unset($_SESSION['admin_user_id']);
    unset($_SESSION['admin_username']);
    unset($_SESSION['admin_user_email']);
    unset($_SESSION['admin_user_name']);
    unset($_SESSION['admin_user_role']);
    unset($_SESSION['admin_user_status']);
    unset($_SESSION['admin_login_time']);
    
    return true;
}

// Check if admin user is logged in
function isAdminUserLoggedIn() {
    return isset($_SESSION['admin_user_id']) && !empty($_SESSION['admin_user_id']);
}

// Require admin login
function requireAdminLogin() {
    if (!isAdminUserLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

// Get admin user by ID
function getAdminUser($user_id) {
    global $conn;
    if (!$conn) return false;
    
    return fetchOne($conn, "SELECT * FROM admin_users WHERE id = ?", [$user_id]);
}

// Update admin user
function updateAdminUser($user_id, $data) {
    global $conn;
    if (!$conn) return false;
    
    // Remove password from data if empty
    if (isset($data['password']) && empty($data['password'])) {
        unset($data['password']);
    } else if (isset($data['password'])) {
        $data['password'] = hashPassword($data['password']);
    }
    
    return updateRecord($conn, 'admin_users', $data, ['id' => $user_id]);
}

// Change admin password
function changeAdminPassword($user_id, $current_password, $new_password) {
    global $conn;
    if (!$conn) return ['success' => false, 'message' => 'Database connection failed'];
    
    // Get current user
    $user = getAdminUser($user_id);
    if (!$user) {
        return ['success' => false, 'message' => 'User not found'];
    }
    
    // Verify current password
    if (!verifyPassword($current_password, $user['password'])) {
        return ['success' => false, 'message' => 'Current password is incorrect'];
    }
    
    // Update password
    $new_password_hash = hashPassword($new_password);
    $result = updateRecord($conn, 'admin_users', ['password' => $new_password_hash], ['id' => $user_id]);
    
    if ($result) {
        return ['success' => true, 'message' => 'Password updated successfully'];
    } else {
        return ['success' => false, 'message' => 'Failed to update password'];
    }
}

// Get all admin users
function getAllAdminUsers() {
    global $conn;
    if (!$conn) return [];
    
    return fetchAll($conn, "SELECT id, username, email, first_name, last_name, role, status, last_login, created_at FROM admin_users ORDER BY created_at DESC");
}

// Setup admin tables
function setupAdminTables() {
    global $conn;
    if (!$conn) return false;

    // Create admin_users table
    $admin_users_sql = "
    CREATE TABLE IF NOT EXISTS admin_users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        role ENUM('super_admin', 'admin', 'editor', 'viewer') DEFAULT 'admin',
        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";

    $result1 = $conn->query($admin_users_sql);

    // Check if contact_messages table exists and add status column if missing
    $contact_table_check = $conn->query("SHOW TABLES LIKE 'contact_messages'");
    if ($contact_table_check && $contact_table_check->num_rows > 0) {
        // Table exists, check if status column exists
        $status_check = $conn->query("SHOW COLUMNS FROM contact_messages LIKE 'status'");
        if (!$status_check || $status_check->num_rows == 0) {
            // Add status column if it doesn't exist
            $conn->query("ALTER TABLE contact_messages ADD COLUMN status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new'");
        }
    } else {
        // Create contact_messages table if it doesn't exist
        $contact_messages_sql = "
        CREATE TABLE IF NOT EXISTS contact_messages (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            email VARCHAR(100) NOT NULL,
            phone VARCHAR(20),
            company VARCHAR(100),
            subject VARCHAR(200) NOT NULL,
            message TEXT NOT NULL,
            service_interest VARCHAR(100),
            business_type VARCHAR(50),
            ip_address VARCHAR(45),
            user_agent TEXT,
            status ENUM('new', 'read', 'replied', 'archived') DEFAULT 'new',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        $conn->query($contact_messages_sql);
    }

    return $result1;
}

// Initialize admin system
function initializeAdminSystem() {
    // Setup tables
    setupAdminTables();
    
    // Create default admin user if none exists
    global $conn;
    $admin_count = fetchOne($conn, "SELECT COUNT(*) as count FROM admin_users");
    
    if ($admin_count['count'] == 0) {
        $default_admin = [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => 'admin',
            'first_name' => 'Admin',
            'last_name' => 'User',
            'role' => 'super_admin',
            'status' => 'active'
        ];
        
        createAdminUser($default_admin);
    }
}
?>
