<?php
// Database configuration
$db_host = 'localhost';
$db_username = 'root';
$db_password = '';
$db_name = 'shiftur_light';


// u975276459_shiftur_light
// Shiftur123
// u975276459_shifturlight
// Create connection
try {
    // First try to connect to the specific database
    $conn = new mysqli($db_host, $db_username, $db_password, $db_name);

    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // Set charset
    $conn->set_charset("utf8mb4");

} catch (Exception $e) {
    // If database doesn't exist, try to connect without database name
    try {
        $conn = new mysqli($db_host, $db_username, $db_password);
        if ($conn->connect_error) {
            throw new Exception("MySQL connection failed: " . $conn->connect_error);
        }

        // Check if database exists
        $result = $conn->query("SHOW DATABASES LIKE '$db_name'");
        if ($result->num_rows == 0) {
            // Database doesn't exist - show setup message
            if (!isset($_SESSION['db_setup_shown'])) {
                $_SESSION['db_setup_shown'] = true;
                $setup_url = (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/setup/install.php';
                if (strpos($_SERVER['REQUEST_URI'], '/admin/') !== false) {
                    $setup_url = str_replace('/admin/', '/', $setup_url);
                }
                echo "<script>
                    if (confirm('Database not found. Would you like to set it up now?')) {
                        window.location.href = '$setup_url';
                    }
                </script>";
            }
        }

        // Set connection to null since database doesn't exist
        $conn = null;

    } catch (Exception $e2) {
        // Log error (in production, log to file instead of displaying)
        error_log("Database connection error: " . $e2->getMessage());

        // Set connection to null so pages can handle gracefully
        $conn = null;
    }
}

// Database helper functions
function executeQuery($conn, $query, $params = []) {
    if (!$conn) return false;
    
    try {
        $stmt = $conn->prepare($query);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }
        
        if (!empty($params)) {
            $types = '';
            foreach ($params as $param) {
                if (is_int($param)) {
                    $types .= 'i';
                } elseif (is_float($param)) {
                    $types .= 'd';
                } else {
                    $types .= 's';
                }
            }
            $stmt->bind_param($types, ...$params);
        }
        
        $stmt->execute();
        return $stmt;
        
    } catch (Exception $e) {
        error_log("Query execution error: " . $e->getMessage());
        return false;
    }
}

function fetchAll($conn, $query, $params = []) {
    $stmt = executeQuery($conn, $query, $params);
    if (!$stmt) return [];
    
    $result = $stmt->get_result();
    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }
    $stmt->close();
    return $data;
}

function fetchOne($conn, $query, $params = []) {
    $stmt = executeQuery($conn, $query, $params);
    if (!$stmt) return null;
    
    $result = $stmt->get_result();
    $data = $result->fetch_assoc();
    $stmt->close();
    return $data;
}

function insertRecord($conn, $table, $data) {
    if (!$conn || empty($data)) return false;
    
    $columns = implode(', ', array_keys($data));
    $placeholders = str_repeat('?,', count($data) - 1) . '?';
    $query = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
    
    $stmt = executeQuery($conn, $query, array_values($data));
    if (!$stmt) return false;
    
    $insertId = $conn->insert_id;
    $stmt->close();
    return $insertId;
}

function updateRecord($conn, $table, $data, $where, $whereParams = []) {
    if (!$conn || empty($data)) return false;
    
    $setParts = [];
    foreach (array_keys($data) as $column) {
        $setParts[] = "{$column} = ?";
    }
    $setClause = implode(', ', $setParts);
    
    $query = "UPDATE {$table} SET {$setClause} WHERE {$where}";
    $params = array_merge(array_values($data), $whereParams);
    
    $stmt = executeQuery($conn, $query, $params);
    if (!$stmt) return false;
    
    $affectedRows = $stmt->affected_rows;
    $stmt->close();
    return $affectedRows;
}

function deleteRecord($conn, $table, $where, $whereParams = []) {
    if (!$conn) return false;
    
    $query = "DELETE FROM {$table} WHERE {$where}";
    $stmt = executeQuery($conn, $query, $whereParams);
    if (!$stmt) return false;
    
    $affectedRows = $stmt->affected_rows;
    $stmt->close();
    return $affectedRows;
}

// Security functions
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Session management
function startSecureSession() {
    if (session_status() === PHP_SESSION_NONE) {
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', 1);
        ini_set('session.use_strict_mode', 1);
        session_start();
    }
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_role']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ../admin/login.php');
        exit;
    }
}

function requireAdmin() {
    if (!isLoggedIn() || $_SESSION['user_role'] !== 'admin') {
        header('Location: ../admin/login.php');
        exit;
    }
}

// CSRF protection
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = generateToken();
    }
    return $_SESSION['csrf_token'];
}

function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// File upload functions
function uploadFile($file, $uploadDir, $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'pdf']) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return ['success' => false, 'message' => 'No file uploaded'];
    }
    
    $fileName = $file['name'];
    $fileSize = $file['size'];
    $fileTmpName = $file['tmp_name'];
    $fileError = $file['error'];
    
    if ($fileError !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'Upload error occurred'];
    }
    
    $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
    
    if (!in_array($fileExt, $allowedTypes)) {
        return ['success' => false, 'message' => 'File type not allowed'];
    }
    
    if ($fileSize > 5 * 1024 * 1024) { // 5MB limit
        return ['success' => false, 'message' => 'File size too large'];
    }
    
    $newFileName = uniqid() . '.' . $fileExt;
    $uploadPath = $uploadDir . '/' . $newFileName;
    
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    if (move_uploaded_file($fileTmpName, $uploadPath)) {
        return ['success' => true, 'filename' => $newFileName, 'path' => $uploadPath];
    } else {
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }
}

// Logging function
function logActivity($conn, $userId, $action, $details = '') {
    if (!$conn) return false;
    
    $data = [
        'user_id' => $userId,
        'action' => $action,
        'details' => $details,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    return insertRecord($conn, 'activity_logs', $data);
}
?>
