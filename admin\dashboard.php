<?php
session_start();
require_once '../includes/admin-auth.php';

// Check if admin user is logged in
requireAdminLogin();

// Get admin user data
$admin_user = getAdminUser($_SESSION['admin_user_id']);

if (!$admin_user) {
    logoutAdminUser();
    header('Location: login.php');
    exit;
}

// Get dashboard statistics
$stats = [];

try {
    // Get website users count
    $website_users = fetchOne($conn, "SELECT COUNT(*) as count FROM website_users WHERE status = 'active'");
    $stats['website_users'] = $website_users['count'] ?? 0;
    
    // Get services count
    $services_result = $conn->query("SHOW TABLES LIKE 'services'");
    if ($services_result && $services_result->num_rows > 0) {
        $services = fetchOne($conn, "SELECT COUNT(*) as count FROM services WHERE status = 'active'");
        $stats['services'] = $services['count'] ?? 0;
    } else {
        $stats['services'] = 0;
    }
    
    // Get portfolio count
    $portfolio_result = $conn->query("SHOW TABLES LIKE 'portfolio'");
    if ($portfolio_result && $portfolio_result->num_rows > 0) {
        $portfolio = fetchOne($conn, "SELECT COUNT(*) as count FROM portfolio WHERE status = 'active'");
        $stats['portfolio'] = $portfolio['count'] ?? 0;
    } else {
        $stats['portfolio'] = 0;
    }
    
    // Get contact messages count
    $messages_result = $conn->query("SHOW TABLES LIKE 'contact_messages'");
    if ($messages_result && $messages_result->num_rows > 0) {
        $new_messages = fetchOne($conn, "SELECT COUNT(*) as count FROM contact_messages WHERE status = 'new'");
        $stats['new_messages'] = $new_messages['count'] ?? 0;

        $total_messages = fetchOne($conn, "SELECT COUNT(*) as count FROM contact_messages");
        $stats['total_messages'] = $total_messages['count'] ?? 0;
    } else {
        $stats['new_messages'] = 0;
        $stats['total_messages'] = 0;
    }
    
} catch (Exception $e) {
    // If there's an error, set default values
    $stats = [
        'website_users' => 0,
        'services' => 0,
        'portfolio' => 0,
        'new_messages' => 0,
        'total_messages' => 0
    ];
}

// Get recent website users
try {
    $recent_users = fetchAll($conn, "SELECT id, username, email, first_name, last_name, created_at FROM website_users ORDER BY created_at DESC LIMIT 5");
} catch (Exception $e) {
    $recent_users = [];
}

$page_title = 'Admin Dashboard - Shiftur';
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8fafc;
            color: #1f2937;
        }
        
        .admin-header {
            background: linear-gradient(135deg, #8b5cf6, #6366f1);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .admin-logo {
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .admin-nav {
            display: flex;
            gap: 2rem;
            align-items: center;
        }
        
        .admin-nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .admin-nav a:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .dashboard-header {
            margin-bottom: 2rem;
        }
        
        .dashboard-title {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .dashboard-subtitle {
            color: #6b7280;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .stat-title {
            font-size: 0.9rem;
            color: #6b7280;
            font-weight: 500;
        }
        
        .stat-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .stat-icon.users { background: linear-gradient(135deg, #10b981, #059669); }
        .stat-icon.services { background: linear-gradient(135deg, #f59e0b, #d97706); }
        .stat-icon.portfolio { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
        .stat-icon.messages { background: linear-gradient(135deg, #ef4444, #dc2626); }
        
        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }
        
        .content-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
        }
        
        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .quick-actions {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            text-decoration: none;
            color: #374151;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: #f1f5f9;
            border-color: #8b5cf6;
            color: #8b5cf6;
        }
        
        .user-list {
            list-style: none;
        }
        
        .user-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .user-item:last-child {
            border-bottom: none;
        }
        
        .user-avatar-small {
            width: 32px;
            height: 32px;
            background: #8b5cf6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .user-details {
            flex: 1;
        }
        
        .user-name {
            font-weight: 500;
            color: #1f2937;
        }
        
        .user-email {
            font-size: 0.8rem;
            color: #6b7280;
        }
        
        .user-date {
            font-size: 0.8rem;
            color: #9ca3af;
        }
        
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .admin-nav {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .quick-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="admin-header">
        <div class="header-content">
            <div class="admin-logo">
                <i class="fas fa-cog"></i> Shiftur Admin
            </div>
            
            <nav class="admin-nav">
                <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="portfolio.php"><i class="fas fa-folder-open"></i> Portfolio</a>
                <a href="users.php"><i class="fas fa-users"></i> Users</a>
                <a href="messages.php"><i class="fas fa-envelope"></i> Messages</a>
                <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
            </nav>
            
            <div class="user-info">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($admin_user['first_name'], 0, 1)); ?>
                </div>
                <div>
                    <div><?php echo htmlspecialchars($admin_user['first_name'] . ' ' . $admin_user['last_name']); ?></div>
                    <div style="font-size: 0.8rem; opacity: 0.8;"><?php echo htmlspecialchars($admin_user['role']); ?></div>
                </div>
                <a href="logout.php" style="color: white; margin-left: 1rem;">
                    <i class="fas fa-sign-out-alt"></i>
                </a>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="dashboard-header">
            <h1 class="dashboard-title">Dashboard</h1>
            <p class="dashboard-subtitle">Welcome back, <?php echo htmlspecialchars($admin_user['first_name']); ?>!</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">Website Users</div>
                    <div class="stat-icon users">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
                <div class="stat-value"><?php echo number_format($stats['website_users']); ?></div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">Services</div>
                    <div class="stat-icon services">
                        <i class="fas fa-cogs"></i>
                    </div>
                </div>
                <div class="stat-value"><?php echo number_format($stats['services']); ?></div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">Portfolio Items</div>
                    <div class="stat-icon portfolio">
                        <i class="fas fa-folder"></i>
                    </div>
                </div>
                <div class="stat-value"><?php echo number_format($stats['portfolio']); ?></div>
            </div>
            
            <div class="stat-card">
                <div class="stat-header">
                    <div class="stat-title">New Messages</div>
                    <div class="stat-icon messages">
                        <i class="fas fa-envelope"></i>
                    </div>
                </div>
                <div class="stat-value"><?php echo number_format($stats['new_messages']); ?></div>
            </div>
        </div>
        
        <div class="content-grid">
            <div class="content-card">
                <h2 class="card-title">
                    <i class="fas fa-bolt"></i>
                    Quick Actions
                </h2>
                
                <div class="quick-actions">
                    <a href="users.php" class="action-btn">
                        <i class="fas fa-users"></i>
                        <span>Manage Users</span>
                    </a>
                    
                    <a href="messages.php" class="action-btn">
                        <i class="fas fa-envelope"></i>
                        <span>View Messages</span>
                    </a>
                    
                    <a href="profile.php" class="action-btn">
                        <i class="fas fa-user-edit"></i>
                        <span>Edit Profile</span>
                    </a>
                    
                    <a href="settings.php" class="action-btn">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </div>
            </div>
            
            <div class="content-card">
                <h2 class="card-title">
                    <i class="fas fa-user-plus"></i>
                    Recent Users
                </h2>
                
                <?php if (!empty($recent_users)): ?>
                    <ul class="user-list">
                        <?php foreach ($recent_users as $user): ?>
                            <li class="user-item">
                                <div class="user-avatar-small">
                                    <?php echo strtoupper(substr($user['first_name'], 0, 1)); ?>
                                </div>
                                <div class="user-details">
                                    <div class="user-name"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                    <div class="user-email"><?php echo htmlspecialchars($user['email']); ?></div>
                                </div>
                                <div class="user-date">
                                    <?php echo date('M d', strtotime($user['created_at'])); ?>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php else: ?>
                    <p style="color: #6b7280; text-align: center; padding: 2rem;">No users found</p>
                <?php endif; ?>
            </div>
        </div>
    </main>
</body>
</html>
