<?php
// Migration script to update budget_range column to business_type
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Migration: Budget Range to Business Type</h1>";

require_once 'includes/db.php';

if (!$conn) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    exit;
}

echo "<p style='color: green;'>✅ Database connected</p>";

try {
    // Check if contact_messages table exists
    $table_check = $conn->query("SHOW TABLES LIKE 'contact_messages'");
    if (!$table_check || $table_check->num_rows == 0) {
        echo "<p style='color: red;'>❌ contact_messages table does not exist</p>";
        exit;
    }

    echo "<p style='color: green;'>✅ contact_messages table exists</p>";

    // Check if budget_range column exists
    $column_check = $conn->query("SHOW COLUMNS FROM contact_messages LIKE 'budget_range'");
    if ($column_check && $column_check->num_rows > 0) {
        echo "<p style='color: orange;'>⚠️ budget_range column exists - migrating to business_type</p>";
        
        // Check if business_type column already exists
        $business_type_check = $conn->query("SHOW COLUMNS FROM contact_messages LIKE 'business_type'");
        if ($business_type_check && $business_type_check->num_rows > 0) {
            echo "<p style='color: orange;'>⚠️ business_type column already exists - dropping budget_range</p>";
            $drop_result = $conn->query("ALTER TABLE contact_messages DROP COLUMN budget_range");
            if ($drop_result) {
                echo "<p style='color: green;'>✅ budget_range column dropped successfully</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to drop budget_range column: " . $conn->error . "</p>";
            }
        } else {
            // Rename budget_range to business_type
            $rename_result = $conn->query("ALTER TABLE contact_messages CHANGE budget_range business_type VARCHAR(50)");
            if ($rename_result) {
                echo "<p style='color: green;'>✅ budget_range column renamed to business_type successfully</p>";
                
                // Update existing data to map budget ranges to business types
                $mapping_updates = [
                    "UPDATE contact_messages SET business_type = 'Startup' WHERE business_type IN ('under-5k', '$1,000 - $5,000', 'Under $5,000')",
                    "UPDATE contact_messages SET business_type = 'Small Business' WHERE business_type IN ('5k-10k', '10k-25k', '$5,000 - $10,000', '$10,000 - $25,000')",
                    "UPDATE contact_messages SET business_type = 'Enterprise' WHERE business_type IN ('25k-50k', 'over-50k', '$25,000 - $50,000', 'Over $50,000')",
                    "UPDATE contact_messages SET business_type = NULL WHERE business_type = '' OR business_type IS NULL"
                ];
                
                foreach ($mapping_updates as $update_sql) {
                    $update_result = $conn->query($update_sql);
                    if ($update_result) {
                        $affected = $conn->affected_rows;
                        if ($affected > 0) {
                            echo "<p style='color: green;'>✅ Updated $affected records</p>";
                        }
                    }
                }
                
                echo "<p style='color: green;'>✅ Data migration completed</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to rename column: " . $conn->error . "</p>";
            }
        }
    } else {
        // Check if business_type column exists
        $business_type_check = $conn->query("SHOW COLUMNS FROM contact_messages LIKE 'business_type'");
        if (!$business_type_check || $business_type_check->num_rows == 0) {
            echo "<p style='color: orange;'>⚠️ Neither budget_range nor business_type column exists - adding business_type</p>";
            $add_result = $conn->query("ALTER TABLE contact_messages ADD COLUMN business_type VARCHAR(50) AFTER service_interest");
            if ($add_result) {
                echo "<p style='color: green;'>✅ business_type column added successfully</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to add business_type column: " . $conn->error . "</p>";
            }
        } else {
            echo "<p style='color: green;'>✅ business_type column already exists</p>";
        }
    }

    // Show current table structure
    echo "<h3>Current Table Structure:</h3>";
    $columns = $conn->query("SHOW COLUMNS FROM contact_messages");
    if ($columns) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $columns->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Show sample data
    echo "<h3>Sample Data (Latest 5 records):</h3>";
    $sample_data = $conn->query("SELECT id, name, email, service_interest, business_type, created_at FROM contact_messages ORDER BY created_at DESC LIMIT 5");
    if ($sample_data && $sample_data->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Service Interest</th><th>Business Type</th><th>Created</th></tr>";
        while ($row = $sample_data->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['email']) . "</td>";
            echo "<td>" . htmlspecialchars($row['service_interest'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['business_type'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($row['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No data found in contact_messages table</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Database Migration - Budget to Business Type</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; margin: 10px 0; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #005a87; }
    </style>
</head>
<body>
    <h2>✅ Database Migration Complete!</h2>
    
    <p>The database has been updated to use business_type instead of budget_range.</p>
    
    <h3>What was changed:</h3>
    <ol>
        <li>✅ <strong>Column renamed/added:</strong> budget_range → business_type</li>
        <li>✅ <strong>Data migrated:</strong> Budget ranges mapped to business types</li>
        <li>✅ <strong>Table structure updated:</strong> Ready for new business type values</li>
    </ol>
    
    <h3>Business Type Values:</h3>
    <ul>
        <li><strong>Startup</strong> - For new businesses and startups</li>
        <li><strong>Small Business</strong> - For established small to medium businesses</li>
        <li><strong>Enterprise</strong> - For large corporations and enterprises</li>
    </ul>
    
    <h3>Next Steps:</h3>
    <p>
        <a href="contact.php" class="btn">Test Contact Form</a>
        <a href="admin/messages.php" class="btn">Check Admin Messages</a>
        <a href="admin/dashboard.php" class="btn">Admin Dashboard</a>
    </p>
    
    <p style="color: green;"><strong>✅ Database migration completed successfully!</strong></p>
    <p style="color: red;"><strong>⚠️ Delete this file after confirming everything works!</strong></p>
</body>
</html>
