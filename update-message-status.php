<?php
// Quick fix to update existing messages with NULL status
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Update Message Status - Quick Fix</h1>";

require_once 'includes/db.php';

if (!$conn) {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    exit;
}

// Check messages with NULL or empty status
try {
    $null_messages = fetchAll($conn, "SELECT id, name, email, subject, status FROM contact_messages WHERE status IS NULL OR status = ''");
    
    echo "<p>Messages with NULL/empty status: <strong>" . count($null_messages) . "</strong></p>";
    
    if (!empty($null_messages)) {
        echo "<h3>Messages to be updated:</h3>";
        echo "<ul>";
        foreach ($null_messages as $msg) {
            echo "<li>ID " . $msg['id'] . ": " . htmlspecialchars($msg['name']) . " - " . htmlspecialchars($msg['subject']) . "</li>";
        }
        echo "</ul>";
        
        // Update all NULL status messages to 'new'
        $update_result = $conn->query("UPDATE contact_messages SET status = 'new' WHERE status IS NULL OR status = ''");
        
        if ($update_result) {
            $affected_rows = $conn->affected_rows;
            echo "<p style='color: green;'>✅ Successfully updated $affected_rows messages to 'new' status!</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to update messages: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ All messages already have status set!</p>";
    }
    
    // Show current status distribution
    echo "<h3>Current Status Distribution:</h3>";
    $status_dist = $conn->query("SELECT status, COUNT(*) as count FROM contact_messages GROUP BY status");
    if ($status_dist) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Status</th><th>Count</th></tr>";
        while ($row = $status_dist->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['status'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($row['count']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Show total count
    $total_count = fetchOne($conn, "SELECT COUNT(*) as count FROM contact_messages");
    echo "<p>Total messages in database: <strong>" . $total_count['count'] . "</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Update Message Status</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 5px; text-decoration: none; display: inline-block; }
        .btn:hover { background: #005a87; }
    </style>
</head>
<body>
    <h2>✅ Status Update Complete!</h2>
    
    <p>The contact form has been fixed and existing messages have been updated.</p>
    
    <h3>What was fixed:</h3>
    <ol>
        <li>✅ <strong>Contact form updated:</strong> Now includes 'status' => 'new' for all submissions</li>
        <li>✅ <strong>Existing messages updated:</strong> All NULL status messages set to 'new'</li>
        <li>✅ <strong>Admin panel ready:</strong> All messages should now appear correctly</li>
    </ol>
    
    <h3>Test the fixes:</h3>
    <p>
        <a href="contact.php" class="btn">Test Contact Form</a>
        <a href="admin/messages.php" class="btn">Check Admin Messages</a>
        <a href="admin/dashboard.php" class="btn">Admin Dashboard</a>
    </p>
    
    <h3>Expected Results:</h3>
    <ul>
        <li>🎯 All existing website messages now visible in admin panel</li>
        <li>🎯 New contact form submissions will appear immediately</li>
        <li>🎯 Message statistics will show correct counts</li>
        <li>🎯 Status management (read, replied, archived) will work</li>
    </ul>
    
    <p style="color: green;"><strong>✅ Contact messages issue should now be resolved!</strong></p>
    <p style="color: red;"><strong>⚠️ Delete this file after confirming everything works!</strong></p>
</body>
</html>
