<?php
// Debug script for live website issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Live Website Debug - shiftur.co</h1>";
echo "<p>Checking account creation issues...</p>";

// Check if we can include the database file
try {
    require_once 'includes/db.php';
    echo "<p style='color: green;'>✅ Database file included successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error including database file: " . $e->getMessage() . "</p>";
    exit;
}

// Check database connection
if ($conn) {
    echo "<p style='color: green;'>✅ Database connection successful</p>";
    echo "<p>Connected to: " . htmlspecialchars($db_name) . "</p>";
} else {
    echo "<p style='color: red;'>❌ Database connection failed</p>";
    echo "<p>Check your hosting database credentials</p>";
    exit;
}

// Check if website_users table exists
try {
    $table_check = $conn->query("SHOW TABLES LIKE 'website_users'");
    if ($table_check && $table_check->num_rows > 0) {
        echo "<p style='color: green;'>✅ website_users table exists</p>";
        
        // Get table structure
        $structure = $conn->query("DESCRIBE website_users");
        if ($structure) {
            echo "<h3>Table Structure:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
            while ($row = $structure->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Check existing users
        $count_result = $conn->query("SELECT COUNT(*) as count FROM website_users");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo "<p>Current users in database: <strong>$count</strong></p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ website_users table does not exist</p>";
        echo "<p>Creating website_users table...</p>";
        
        // Create the table
        $create_table_sql = "
        CREATE TABLE website_users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            phone VARCHAR(20),
            company VARCHAR(100),
            status ENUM('active', 'inactive', 'pending') DEFAULT 'active',
            email_verified BOOLEAN DEFAULT FALSE,
            verification_token VARCHAR(255),
            reset_token VARCHAR(255),
            reset_token_expires TIMESTAMP NULL,
            last_login TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if ($conn->query($create_table_sql)) {
            echo "<p style='color: green;'>✅ website_users table created successfully</p>";
        } else {
            echo "<p style='color: red;'>❌ Error creating table: " . $conn->error . "</p>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking table: " . $e->getMessage() . "</p>";
}

// Check if admin_users table exists
try {
    $admin_table_check = $conn->query("SHOW TABLES LIKE 'admin_users'");
    if ($admin_table_check && $admin_table_check->num_rows > 0) {
        echo "<p style='color: green;'>✅ admin_users table exists</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ admin_users table does not exist</p>";
        echo "<p>You may need to run the admin setup</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking admin table: " . $e->getMessage() . "</p>";
}

// Check if contact_messages table exists
try {
    $contact_table_check = $conn->query("SHOW TABLES LIKE 'contact_messages'");
    if ($contact_table_check && $contact_table_check->num_rows > 0) {
        echo "<p style='color: green;'>✅ contact_messages table exists</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ contact_messages table does not exist</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error checking contact table: " . $e->getMessage() . "</p>";
}

// Test account creation functionality
echo "<h2>Test Account Creation</h2>";

if ($_POST && isset($_POST['test_signup'])) {
    $test_username = 'testuser_' . time();
    $test_email = 'test_' . time() . '@example.com';
    $test_password = 'TestPassword123';
    
    echo "<p>Testing account creation with:</p>";
    echo "<p>Username: " . htmlspecialchars($test_username) . "</p>";
    echo "<p>Email: " . htmlspecialchars($test_email) . "</p>";
    
    try {
        // Check if website-auth.php exists
        if (file_exists('includes/website-auth.php')) {
            require_once 'includes/website-auth.php';
            echo "<p style='color: green;'>✅ website-auth.php loaded</p>";
            
            // Test user creation
            $result = createWebsiteUser([
                'username' => $test_username,
                'email' => $test_email,
                'password' => $test_password,
                'first_name' => 'Test',
                'last_name' => 'User'
            ]);
            
            if ($result['success']) {
                echo "<p style='color: green;'>✅ Test account created successfully! ID: " . $result['user_id'] . "</p>";
                
                // Clean up test user
                $conn->query("DELETE FROM website_users WHERE id = " . $result['user_id']);
                echo "<p>Test user cleaned up</p>";
            } else {
                echo "<p style='color: red;'>❌ Account creation failed: " . $result['message'] . "</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ website-auth.php file not found</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error during test: " . $e->getMessage() . "</p>";
    }
}

// Check PHP configuration
echo "<h2>PHP Configuration</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>MySQL Extension: " . (extension_loaded('mysqli') ? '✅ Available' : '❌ Missing') . "</p>";
echo "<p>Password Hashing: " . (function_exists('password_hash') ? '✅ Available' : '❌ Missing') . "</p>";
echo "<p>Session Support: " . (function_exists('session_start') ? '✅ Available' : '❌ Missing') . "</p>";

// Check file permissions
echo "<h2>File Permissions</h2>";
$files_to_check = [
    'includes/db.php',
    'includes/website-auth.php',
    'signup.php',
    'login.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        $perms = fileperms($file);
        echo "<p>" . htmlspecialchars($file) . ": " . substr(sprintf('%o', $perms), -4) . " ✅</p>";
    } else {
        echo "<p>" . htmlspecialchars($file) . ": ❌ File not found</p>";
    }
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>Live Site Debug - shiftur.co</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 0; }
        .btn:hover { background: #005a87; }
    </style>
</head>
<body>
    <h2>Test Account Creation</h2>
    <form method="POST">
        <button type="submit" name="test_signup" class="btn">Test Signup Process</button>
    </form>
    
    <h2>Common Issues & Solutions</h2>
    <ul>
        <li><strong>Database Connection:</strong> Verify hosting database credentials</li>
        <li><strong>Missing Tables:</strong> Run setup scripts to create required tables</li>
        <li><strong>File Permissions:</strong> Ensure PHP files have proper read permissions</li>
        <li><strong>PHP Extensions:</strong> Verify mysqli and other required extensions are enabled</li>
        <li><strong>Error Logging:</strong> Check hosting error logs for detailed error messages</li>
    </ul>
    
    <h2>Next Steps</h2>
    <ol>
        <li>If tables are missing, run the setup scripts</li>
        <li>Test the signup form manually</li>
        <li>Check hosting error logs for specific errors</li>
        <li>Verify all required files are uploaded to hosting</li>
    </ol>
    
    <p><a href="signup.php">Test Signup Page</a> | <a href="login.php">Test Login Page</a></p>
</body>
</html>
