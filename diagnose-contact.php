<?php
// Diagnose contact form issues
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Contact Form Diagnosis</h1>";

// Test 1: Include files
echo "<h2>1. Testing Includes</h2>";
try {
    require_once 'includes/website-auth.php';
    echo "<p style='color: green;'>✅ website-auth.php included successfully</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error including website-auth.php: " . $e->getMessage() . "</p>";
    exit;
}

// Test 2: Database connection
echo "<h2>2. Testing Database Connection</h2>";
if (isset($conn) && $conn) {
    echo "<p style='color: green;'>✅ Database connection available</p>";
    echo "<p>Connection type: " . get_class($conn) . "</p>";
} else {
    echo "<p style='color: red;'>❌ Database connection not available</p>";
    echo "<p>conn variable: " . (isset($conn) ? 'set but false/null' : 'not set') . "</p>";
}

// Test 3: Functions availability
echo "<h2>3. Testing Functions</h2>";
$functions = ['sanitizeInput', 'validateEmail', 'insertRecord', 'fetchOne', 'fetchAll'];
foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "<p style='color: green;'>✅ $func() function exists</p>";
    } else {
        echo "<p style='color: red;'>❌ $func() function missing</p>";
    }
}

// Test 4: Table existence
echo "<h2>4. Testing Table Existence</h2>";
if ($conn) {
    try {
        $table_check = $conn->query("SHOW TABLES LIKE 'contact_messages'");
        if ($table_check && $table_check->num_rows > 0) {
            echo "<p style='color: green;'>✅ contact_messages table exists</p>";
            
            // Check table structure
            $columns = $conn->query("SHOW COLUMNS FROM contact_messages");
            if ($columns) {
                echo "<p>Table columns:</p><ul>";
                while ($row = $columns->fetch_assoc()) {
                    echo "<li>" . htmlspecialchars($row['Field']) . " (" . htmlspecialchars($row['Type']) . ")</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p style='color: red;'>❌ contact_messages table does not exist</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error checking table: " . $e->getMessage() . "</p>";
    }
}

// Test 5: Insert test
echo "<h2>5. Testing Insert Functionality</h2>";
if ($conn && function_exists('insertRecord')) {
    $test_data = [
        'name' => 'Diagnosis Test',
        'email' => '<EMAIL>',
        'subject' => 'Diagnosis Test - ' . date('H:i:s'),
        'message' => 'This is a diagnosis test message',
        'business_type' => 'Small Business',
        'status' => 'new',
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
        'user_agent' => 'Diagnosis Script'
    ];
    
    try {
        $result = insertRecord($conn, 'contact_messages', $test_data);
        if ($result) {
            echo "<p style='color: green;'>✅ Insert test successful! ID: $result</p>";
            
            // Verify the insert
            if (function_exists('fetchOne')) {
                $verify = fetchOne($conn, "SELECT * FROM contact_messages WHERE id = ?", [$result]);
                if ($verify) {
                    echo "<p style='color: green;'>✅ Insert verified in database</p>";
                    echo "<p>Inserted: " . htmlspecialchars($verify['name']) . " - " . htmlspecialchars($verify['subject']) . "</p>";
                } else {
                    echo "<p style='color: red;'>❌ Could not verify insert</p>";
                }
            }
        } else {
            echo "<p style='color: red;'>❌ Insert test failed</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Insert error: " . $e->getMessage() . "</p>";
    }
}

// Test 6: Contact form simulation
echo "<h2>6. Testing Contact Form Logic</h2>";
if ($_POST && isset($_POST['test_contact'])) {
    echo "<p>Simulating contact form submission...</p>";
    
    // Simulate the exact contact.php logic
    $name = sanitizeInput($_POST['name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $company = sanitizeInput($_POST['company'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $message = sanitizeInput($_POST['message'] ?? '');
    $service_interest = sanitizeInput($_POST['service_interest'] ?? '');
    $business_type = sanitizeInput($_POST['business_type'] ?? '');
    
    echo "<p>Sanitized data:</p>";
    echo "<ul>";
    echo "<li>Name: " . htmlspecialchars($name) . "</li>";
    echo "<li>Email: " . htmlspecialchars($email) . "</li>";
    echo "<li>Subject: " . htmlspecialchars($subject) . "</li>";
    echo "<li>Business Type: " . htmlspecialchars($business_type) . "</li>";
    echo "</ul>";
    
    // Validation
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        echo "<p style='color: red;'>❌ Validation failed: Missing required fields</p>";
    } elseif (!validateEmail($email)) {
        echo "<p style='color: red;'>❌ Validation failed: Invalid email</p>";
    } else {
        echo "<p style='color: green;'>✅ Validation passed</p>";
        
        // Save to database
        if ($conn) {
            $data = [
                'name' => $name,
                'email' => $email,
                'phone' => $phone,
                'company' => $company,
                'subject' => $subject,
                'message' => $message,
                'service_interest' => $service_interest,
                'business_type' => $business_type,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'status' => 'new'
            ];
            
            $result = insertRecord($conn, 'contact_messages', $data);
            if ($result) {
                echo "<p style='color: green;'>✅ Contact form simulation successful! ID: $result</p>";
            } else {
                echo "<p style='color: red;'>❌ Contact form simulation failed</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ No database connection</p>";
        }
    }
}

// Show current message count
if ($conn) {
    try {
        $count_result = $conn->query("SELECT COUNT(*) as count FROM contact_messages");
        if ($count_result) {
            $count = $count_result->fetch_assoc()['count'];
            echo "<p><strong>Total messages in database: $count</strong></p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error getting count: " . $e->getMessage() . "</p>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Contact Form Diagnosis</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        label { display: block; margin-bottom: 5px; }
        input, select, textarea { width: 300px; padding: 5px; }
        .btn { padding: 10px 20px; background: #007cba; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 10px 0; }
    </style>
</head>
<body>
    <h2>Test Contact Form Submission</h2>
    <form method="POST">
        <div class="form-group">
            <label>Name *</label>
            <input type="text" name="name" value="Test User" required>
        </div>
        <div class="form-group">
            <label>Email *</label>
            <input type="email" name="email" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label>Subject *</label>
            <input type="text" name="subject" value="Test Subject" required>
        </div>
        <div class="form-group">
            <label>Business Type</label>
            <select name="business_type">
                <option value="">Select type</option>
                <option value="Startup">Startup</option>
                <option value="Small Business" selected>Small Business</option>
                <option value="Enterprise">Enterprise</option>
            </select>
        </div>
        <div class="form-group">
            <label>Message *</label>
            <textarea name="message" required>This is a test message for diagnosis.</textarea>
        </div>
        <button type="submit" name="test_contact" class="btn">Test Contact Form</button>
    </form>
    
    <h3>Quick Links:</h3>
    <p>
        <a href="contact.php" class="btn">Real Contact Form</a>
        <a href="admin/messages.php" class="btn">Admin Messages</a>
        <a href="simple-contact-test.php" class="btn">Simple Test</a>
    </p>
</body>
</html>
